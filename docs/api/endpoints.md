# API Reference - Endpoints del Agente

Esta documentación describe todos los endpoints disponibles en el agente LightRAG desplegado en SageMaker.

## Base URL

```
https://runtime.sagemaker.{region}.amazonaws.com/endpoints/{endpoint-name}/invocations
```

## Autenticación

Todas las llamadas requieren autenticación AWS usando AWS Signature Version 4.

```python
import boto3

# Usando boto3
runtime = boto3.client('sagemaker-runtime', region_name='us-east-1')
response = runtime.invoke_endpoint(
    EndpointName='lightrag-agent-endpoint-TIMESTAMP',
    ContentType='application/json',
    Body=json.dumps(payload)
)
```

## Endpoints

### POST /invocations

Endpoint principal para realizar consultas al agente.

#### Request

```json
{
  "question": "string",     // Pregunta a realizar (requerido)
  "mode": "string",         // Modo de búsqueda (opcional, default: "hybrid")
  "use_adk": boolean        // Usar integración ADK (opcional, default: false)
}
```

#### Parámetros

- **question** (string, requerido): La pregunta o consulta a realizar
- **mode** (string, opcional): Modo de búsqueda
  - `"naive"`: Búsqueda simple por similitud
  - `"local"`: Búsqueda en contexto local
  - `"global"`: Búsqueda en todo el grafo
  - `"hybrid"`: Combinación local + global (recomendado)
- **use_adk** (boolean, opcional): Si usar la integración con Google ADK

#### Response

```json
{
  "response": "string",     // Respuesta generada por el agente
  "question": "string",     // Pregunta original
  "mode": "string",         // Modo utilizado
  "use_adk": boolean        // Si se usó ADK
}
```

#### Ejemplo

```python
import boto3
import json

runtime = boto3.client('sagemaker-runtime', region_name='us-east-1')

payload = {
    "question": "¿Qué es LightRAG y cómo funciona?",
    "mode": "hybrid",
    "use_adk": False
}

response = runtime.invoke_endpoint(
    EndpointName='lightrag-agent-endpoint-20241207-143022',
    ContentType='application/json',
    Body=json.dumps(payload)
)

result = json.loads(response['Body'].read().decode())
print(result['response'])
```

### POST /load_documents

Carga nuevos documentos en la base de conocimiento.

#### Request

```json
{
  "docs_path": "string"     // Ruta a los documentos (opcional)
}
```

#### Response

```json
{
  "success": boolean,       // Si la carga fue exitosa
  "message": "string",      // Mensaje descriptivo
  "docs_path": "string"     // Ruta utilizada
}
```

### GET /graph_info

Obtiene información sobre el grafo de conocimiento.

#### Response

```json
{
  "status": "string",       // Estado del grafo
  "working_dir": "string",  // Directorio de trabajo
  "message": "string"       // Información adicional
}
```

### GET /ping

Health check del endpoint.

#### Response

```json
{
  "status": "healthy"
}
```

## Códigos de Estado HTTP

- **200 OK**: Solicitud exitosa
- **400 Bad Request**: Parámetros inválidos
- **500 Internal Server Error**: Error interno del servidor

## Ejemplos de Uso

### Consulta Básica

```python
payload = {
    "question": "¿Cuáles son las funcionalidades principales del agente?"
}
```

### Consulta con Modo Específico

```python
payload = {
    "question": "Explica la arquitectura del sistema",
    "mode": "global"
}
```

### Consulta con ADK

```python
payload = {
    "question": "Analiza los documentos técnicos disponibles",
    "mode": "hybrid",
    "use_adk": True
}
```

### Cargar Documentos

```python
payload = {
    "docs_path": "/opt/ml/input/data/documents"
}

response = runtime.invoke_endpoint(
    EndpointName=endpoint_name,
    ContentType='application/json',
    Body=json.dumps(payload),
    # Usar endpoint específico para carga de documentos
    InvocationType='RequestResponse'
)
```

## Límites y Consideraciones

### Límites de Tamaño

- **Pregunta máxima**: 8,000 caracteres
- **Respuesta máxima**: 16,000 caracteres
- **Timeout**: 30 segundos por consulta

### Rendimiento

- **Consultas concurrentes**: Hasta 10 por instancia
- **Tiempo de respuesta típico**: 2-5 segundos
- **Modo más rápido**: `naive`
- **Modo más completo**: `hybrid`

### Costos

- **Instancia ml.t3.medium**: ~$0.05/hora
- **Instancia ml.m5.large**: ~$0.10/hora
- **Llamadas a OpenAI**: Variable según uso

## Manejo de Errores

### Errores Comunes

```json
// Error: Pregunta vacía
{
  "error": "Question is required"
}

// Error: Agente no inicializado
{
  "error": "Agent not initialized"
}

// Error: Modo inválido
{
  "error": "Invalid mode: xyz"
}
```

### Reintentos

Se recomienda implementar reintentos con backoff exponencial:

```python
import time
import random

def query_with_retry(payload, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = runtime.invoke_endpoint(...)
            return response
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            time.sleep(wait_time)
```

## Monitoreo

### Métricas Disponibles

- **Invocations**: Número de llamadas
- **ModelLatency**: Latencia del modelo
- **OverheadLatency**: Latencia de overhead
- **Errors**: Número de errores

### CloudWatch Logs

Los logs están disponibles en:
```
/aws/sagemaker/Endpoints/{endpoint-name}
```

## Versionado

La API sigue versionado semántico. La versión actual es `1.0.0`.

### Cambios de Versión

- **1.0.0**: Versión inicial con funcionalidad completa
- **1.1.0**: Agregado soporte para ADK (próximamente)
- **1.2.0**: Optimizaciones de rendimiento (próximamente)

---

Para más información, consulta la [documentación técnica](../technical/arquitectura.md) o los [ejemplos de uso](../examples/).
