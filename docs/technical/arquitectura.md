# Arquitectura del Agente

## Componentes Principales

### 1. LightRAG Core
- **Base de Datos Vectorial**: Almacena embeddings de documentos
- **Base de Datos de Grafos**: Mantiene relaciones entre entidades
- **Motor de Búsqueda**: Combina búsqueda vectorial y de grafos

### 2. Google ADK Integration
- **Herramientas de Google**: Integración con servicios de Google Cloud
- **APIs**: Acceso a servicios externos
- **Autenticación**: Manejo seguro de credenciales

### 3. AWS SageMaker Deployment
- **Contenedor Docker**: Empaquetado para despliegue
- **Endpoints**: APIs REST para interacción
- **Escalabilidad**: Auto-scaling basado en demanda

## Flujo de Datos

1. **Ingesta**: Documentos → Procesamiento → Embeddings
2. **Indexación**: Embeddings → Base Vectorial + Grafo de Conocimiento
3. **Consulta**: Pregunta → Búsqueda Híbrida → Respuesta Contextual

## Tecnologías Utilizadas

- **LightRAG**: Framework principal para RAG
- **Google ADK**: Kit de desarrollo de agentes
- **AWS SageMaker**: Plataforma de despliegue
- **Vector Database**: Almacenamiento de embeddings
- **Graph Database**: Relaciones y entidades
