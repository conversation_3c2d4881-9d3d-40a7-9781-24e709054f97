# Guía de Inicio Rápido

Esta guía te ayudará a poner en funcionamiento el agente LightRAG en pocos minutos.

## 🚀 Inicio Rápido (5 minutos)

### 1. Configuración Inicial

```bash
# Activar entorno conda
source ~/miniconda3/bin/activate

# Verificar instalación de dependencias
python -c "import lightrag; print('LightRAG instalado correctamente')"
```

### 2. Configurar Variables de Entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar con tu API key de OpenAI
echo "OPENAI_API_KEY=tu_api_key_aqui" > .env
```

### 3. Prueba Rápida

```bash
# Ejecutar pruebas básicas
python test_agent.py
```

### 4. Modo Interactivo

```bash
# Iniciar agente en modo interactivo
python -m agent.main
```

## 📖 Primeros Pasos

### Cargar Documentos

El agente automáticamente carga documentos desde la carpeta `docs/`. Puedes agregar tus propios documentos:

```bash
# Agregar documentos markdown
echo "# Mi Documento\nEste es contenido de ejemplo." > docs/mi_documento.md

# Reiniciar agente para cargar nuevos documentos
python -m agent.main
```

### Realizar Consultas

Una vez iniciado el agente, puedes hacer preguntas:

```
Pregunta: ¿Qué documentos tienes disponibles?
Modo (hybrid): 

Pregunta: Explica la arquitectura del sistema
Modo (hybrid): local

Pregunta: ¿Cómo funciona LightRAG?
Modo (hybrid): global
```

### Modos de Búsqueda

- **hybrid** (recomendado): Combina búsqueda local y global
- **local**: Busca en contexto local específico
- **global**: Busca en todo el grafo de conocimiento
- **naive**: Búsqueda simple por similitud

## 🔧 Configuración Avanzada

### Personalizar Configuración

Edita `agent/config/lightrag_config.py`:

```python
# Cambiar modelo de LLM
LLM_CONFIG = {
    "model": "gpt-4",  # Cambiar a GPT-4
    "temperature": 0.2,  # Más creativo
    "max_tokens": 8000,  # Respuestas más largas
}

# Configurar embeddings
EMBEDDING_CONFIG = {
    "model": "text-embedding-3-large",  # Modelo más potente
    "dimensions": 3072,  # Más dimensiones
}
```

### Agregar Documentos Personalizados

```bash
# Crear estructura personalizada
mkdir -p docs/mi_proyecto/{api,guias,ejemplos}

# Agregar documentación de API
echo "# API Reference\n..." > docs/mi_proyecto/api/endpoints.md

# Agregar guías
echo "# Guía de Usuario\n..." > docs/mi_proyecto/guias/usuario.md
```

## 🌐 Despliegue Rápido en SageMaker

### Prerrequisitos

1. AWS CLI configurado con perfil "IA"
2. Rol de SageMaker creado
3. Docker instalado

### Despliegue en 3 Pasos

```bash
# 1. Verificar configuración AWS
aws sts get-caller-identity --profile IA

# 2. Desplegar (reemplaza YOUR-ACCOUNT con tu ID de cuenta)
python deployment/deploy_sagemaker.py \
    --profile IA \
    --role-arn arn:aws:iam::YOUR-ACCOUNT:role/SageMakerRole

# 3. Probar endpoint (usa el nombre generado)
python deployment/test_endpoint.py \
    --endpoint-name lightrag-agent-endpoint-TIMESTAMP \
    --question "¿Cómo estás?"
```

## 🐛 Solución de Problemas Comunes

### Error: "OPENAI_API_KEY no configurada"

```bash
# Verificar variable de entorno
echo $OPENAI_API_KEY

# Si está vacía, configurar:
export OPENAI_API_KEY="tu_api_key_aqui"
```

### Error: "No module named lightrag"

```bash
# Reinstalar dependencias
source ~/miniconda3/bin/activate
python -m pip install lightrag chromadb neo4j openai
```

### Error: "Directorio docs no encontrado"

```bash
# Crear estructura de documentos
mkdir -p docs/{api,guides,technical,examples}
echo "# Documento de ejemplo" > docs/examples/ejemplo.md
```

### Error en Despliegue de SageMaker

```bash
# Verificar permisos AWS
aws iam get-role --role-name SageMakerRole --profile IA

# Verificar Docker
docker --version
docker ps
```

## 📊 Monitoreo y Logs

### Logs Locales

```bash
# Ver logs del agente
tail -f agent/lightrag/working_dir/agent.log

# Logs con más detalle
export PYTHONPATH=. && python -m agent.main --verbose
```

### Logs de SageMaker

```bash
# Ver logs del endpoint
aws logs describe-log-groups --profile IA | grep lightrag

# Seguir logs en tiempo real
aws logs tail /aws/sagemaker/Endpoints/lightrag-agent-endpoint-TIMESTAMP --follow --profile IA
```

## 🎯 Próximos Pasos

1. **Personalizar Documentos**: Agrega tu propia documentación
2. **Ajustar Configuración**: Optimiza parámetros para tu caso de uso
3. **Integrar con ADK**: Explora funcionalidades avanzadas
4. **Escalar en SageMaker**: Configura auto-scaling y múltiples instancias
5. **Monitorear Rendimiento**: Implementa métricas y alertas

## 📚 Recursos Adicionales

- [Documentación Técnica](../technical/arquitectura.md)
- [Ejemplos Avanzados](../examples/)
- [Configuración de AWS](aws_setup.md)
- [Optimización de Rendimiento](performance.md)

---

¿Necesitas ayuda? Consulta la documentación completa o ejecuta las pruebas para verificar tu configuración.
