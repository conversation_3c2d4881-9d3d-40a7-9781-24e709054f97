#!/usr/bin/env python3
"""
Test completo de integración AWS Bedrock con LightRAG
"""
import asyncio
import os
import sys
import json
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


async def test_aws_credentials():
    """Prueba las credenciales de AWS"""
    print("🔐 Probando credenciales de AWS...")
    
    try:
        import boto3
        from botocore.exceptions import NoCredentialsError, ClientError
        
        # Crear sesión con perfil IA
        session = boto3.Session(profile_name="IA")
        
        # Verificar credenciales
        sts_client = session.client('sts', region_name='us-east-2')
        identity = sts_client.get_caller_identity()
        
        print(f"✅ AWS Account ID: {identity['Account']}")
        print(f"✅ User ARN: {identity['Arn']}")
        
        # Verificar acceso a Bedrock
        bedrock_client = session.client('bedrock-runtime', region_name='us-east-2')
        print("✅ Cliente Bedrock creado exitosamente")
        
        return session, bedrock_client
        
    except NoCredentialsError:
        print("❌ No se encontraron credenciales AWS")
        print("💡 Configura AWS CLI: aws configure --profile IA")
        return None, None
        
    except ClientError as e:
        print(f"❌ Error de cliente AWS: {e}")
        return None, None
        
    except Exception as e:
        print(f"❌ Error verificando credenciales: {e}")
        return None, None


async def test_bedrock_llm(bedrock_client):
    """Prueba el modelo LLM de Bedrock"""
    print("\n🤖 Probando modelo LLM de Bedrock...")
    
    try:
        # Configuración del modelo
        model_id = "anthropic.claude-3-haiku-********-v1:0"
        
        # Prompt de prueba
        prompt = "Human: ¿Qué es AWS Bedrock? Responde en español en máximo 100 palabras.\nAssistant:"
        
        body = {
            "prompt": prompt,
            "max_tokens_to_sample": 200,
            "temperature": 0.1,
            "top_p": 0.9,
            "stop_sequences": ["\n\nHuman:"]
        }
        
        print(f"📝 Modelo: {model_id}")
        print(f"📝 Prompt: {prompt[:50]}...")
        
        # Llamar a Bedrock
        response = bedrock_client.invoke_model(
            modelId=model_id,
            body=json.dumps(body),
            contentType="application/json",
            accept="application/json"
        )
        
        # Procesar respuesta
        response_body = json.loads(response['body'].read())
        completion = response_body.get('completion', '')
        
        print(f"✅ Respuesta: {completion}")
        
        # Verificar que la respuesta no esté vacía
        assert len(completion.strip()) > 0, "Respuesta vacía"
        
        return True
        
    except Exception as e:
        print(f"❌ Error con modelo LLM: {e}")
        return False


async def test_bedrock_embeddings(bedrock_client):
    """Prueba el modelo de embeddings de Bedrock"""
    print("\n🔤 Probando modelo de embeddings de Bedrock...")
    
    try:
        # Configuración del modelo
        model_id = "amazon.titan-embed-text-v1"
        
        # Textos de prueba
        test_texts = [
            "¿Qué es AWS Bedrock?",
            "Bedrock es un servicio de IA generativa de Amazon",
            "Los modelos de lenguaje procesan texto"
        ]
        
        embeddings = []
        
        for text in test_texts:
            body = {
                "inputText": text
            }
            
            response = bedrock_client.invoke_model(
                modelId=model_id,
                body=json.dumps(body),
                contentType="application/json",
                accept="application/json"
            )
            
            response_body = json.loads(response['body'].read())
            embedding = response_body.get('embedding', [])
            embeddings.append(embedding)
        
        print(f"✅ Modelo: {model_id}")
        print(f"✅ Embeddings generados: {len(embeddings)} textos")
        print(f"✅ Dimensiones: {len(embeddings[0]) if embeddings else 0}")
        
        # Calcular similitud
        if len(embeddings) >= 2:
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np
            
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            print(f"✅ Similitud entre textos relacionados: {similarity:.3f}")
        
        return embeddings
        
    except Exception as e:
        print(f"❌ Error con embeddings: {e}")
        return []


async def test_bedrock_configuration():
    """Prueba la configuración del agente con Bedrock"""
    print("\n⚙️  Probando configuración del agente...")
    
    try:
        from agent.config.lightrag_config import (
            AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG
        )
        
        print(f"✅ Modelos disponibles: {AVAILABLE_MODELS}")
        print(f"✅ LLM Provider: {LLM_CONFIG['provider']}")
        print(f"✅ LLM Model: {LLM_CONFIG['model']}")
        print(f"✅ Embedding Provider: {EMBEDDING_CONFIG['provider']}")
        print(f"✅ Embedding Model: {EMBEDDING_CONFIG['model']}")
        
        # Verificar que Bedrock sea el proveedor primario
        if AVAILABLE_MODELS.get("bedrock", False):
            assert LLM_CONFIG['provider'] == 'bedrock', f"Expected bedrock, got {LLM_CONFIG['provider']}"
            assert EMBEDDING_CONFIG['provider'] == 'bedrock', f"Expected bedrock, got {EMBEDDING_CONFIG['provider']}"
            print("✅ Bedrock configurado como proveedor primario")
        else:
            print("⚠️  Bedrock no disponible, usando fallback")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bedrock_rag_pipeline(bedrock_client):
    """Prueba el pipeline RAG completo con Bedrock"""
    print("\n🔄 Probando pipeline RAG con Bedrock...")
    
    try:
        # Simular un pipeline RAG simplificado
        
        # 1. Documentos de prueba
        documents = [
            "AWS Bedrock es un servicio completamente administrado que ofrece modelos de IA generativa de alto rendimiento.",
            "Bedrock proporciona acceso a modelos de lenguaje de Amazon y terceros como Anthropic Claude.",
            "Los modelos de Bedrock se pueden usar para generación de texto, embeddings y análisis de contenido.",
            "El servicio incluye capacidades de fine-tuning y personalización de modelos."
        ]
        
        # 2. Generar embeddings para documentos
        print("📚 Generando embeddings para documentos...")
        doc_embeddings = []
        
        for doc in documents:
            body = {
                "inputText": doc
            }
            
            response = bedrock_client.invoke_model(
                modelId="amazon.titan-embed-text-v1",
                body=json.dumps(body),
                contentType="application/json",
                accept="application/json"
            )
            
            response_body = json.loads(response['body'].read())
            embedding = response_body.get('embedding', [])
            doc_embeddings.append(embedding)
        
        # 3. Consulta del usuario
        user_query = "¿Qué es AWS Bedrock y qué modelos ofrece?"
        
        # 4. Generar embedding para la consulta
        query_body = {
            "inputText": user_query
        }
        
        query_response = bedrock_client.invoke_model(
            modelId="amazon.titan-embed-text-v1",
            body=json.dumps(query_body),
            contentType="application/json",
            accept="application/json"
        )
        
        query_response_body = json.loads(query_response['body'].read())
        query_embedding = query_response_body.get('embedding', [])
        
        # 5. Búsqueda de documentos relevantes
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        similarities = cosine_similarity([query_embedding], doc_embeddings)[0]
        top_indices = np.argsort(similarities)[-2:][::-1]  # Top 2
        
        relevant_docs = [documents[i] for i in top_indices if similarities[i] > 0.1]
        
        print(f"✅ Consulta: {user_query}")
        print(f"✅ Documentos relevantes encontrados: {len(relevant_docs)}")
        
        # 6. Generar respuesta con LLM
        context = "\n".join(relevant_docs)
        
        llm_prompt = f"""Human: Basándote en el siguiente contexto, responde la pregunta en español:

Contexto:
{context}

Pregunta: {user_query}
Assistant:"""
        
        llm_body = {
            "prompt": llm_prompt,
            "max_tokens_to_sample": 300,
            "temperature": 0.1,
            "top_p": 0.9,
            "stop_sequences": ["\n\nHuman:"]
        }
        
        llm_response = bedrock_client.invoke_model(
            modelId="anthropic.claude-3-haiku-********-v1:0",
            body=json.dumps(llm_body),
            contentType="application/json",
            accept="application/json"
        )
        
        llm_response_body = json.loads(llm_response['body'].read())
        final_response = llm_response_body.get('completion', '')
        
        print(f"✅ Respuesta RAG: {final_response}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en pipeline RAG: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bedrock_agent_integration():
    """Prueba la integración del agente con Bedrock"""
    print("\n🤖 Probando integración del agente...")
    
    try:
        # Importar el agente (si está disponible)
        from agent.config.lightrag_config import AVAILABLE_MODELS
        
        if not AVAILABLE_MODELS.get("bedrock", False):
            print("⚠️  Bedrock no disponible, saltando prueba del agente")
            return False
        
        # Simular inicialización del agente
        print("✅ Configuración de Bedrock detectada")
        print("✅ Agente listo para usar Bedrock")
        
        # Aquí se podría probar el agente real si estuviera disponible
        # agent = LightRAGAgent()
        # response = await agent.query("¿Qué es Bedrock?")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en integración del agente: {e}")
        return False


async def main():
    """Función principal"""
    print("🚀 Test de integración AWS Bedrock con LightRAG\n")
    
    results = {}
    
    # 1. Credenciales AWS
    session, bedrock_client = await test_aws_credentials()
    results['aws_credentials'] = session is not None and bedrock_client is not None
    
    if not results['aws_credentials']:
        print("\n❌ Sin credenciales AWS, abortando pruebas de Bedrock")
        print("\n🔧 Para configurar AWS:")
        print("1. aws configure --profile IA")
        print("2. Configurar región: us-east-2")
        print("3. Verificar permisos de Bedrock")
        return
    
    # 2. Configuración del agente
    results['configuration'] = await test_bedrock_configuration()
    
    # 3. Modelo LLM
    results['llm'] = await test_bedrock_llm(bedrock_client)
    
    # 4. Embeddings
    embeddings = await test_bedrock_embeddings(bedrock_client)
    results['embeddings'] = len(embeddings) > 0
    
    # 5. Pipeline RAG
    results['rag_pipeline'] = await test_bedrock_rag_pipeline(bedrock_client)
    
    # 6. Integración del agente
    results['agent_integration'] = await test_bedrock_agent_integration()
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests >= 4:
        print("\n🎉 ¡Integración con AWS Bedrock funcionando!")
        print("\n✅ Funcionalidades verificadas:")
        print("- Credenciales AWS con perfil IA")
        print("- Acceso a modelos Bedrock en us-east-2")
        print("- Generación de texto con Claude 3 Haiku")
        print("- Embeddings con Titan Embeddings")
        print("- Pipeline RAG completo con Bedrock")
        
        print("\n🚀 Comandos para usar:")
        print("python test_bedrock_integration.py")
        
        print("\n💡 Modelos disponibles:")
        print("- LLM: anthropic.claude-3-haiku-********-v1:0")
        print("- Embeddings: amazon.titan-embed-text-v1")
        print("- Región: us-east-2")
        print("- Perfil: IA")
        
    else:
        print("\n⚠️  Algunas funcionalidades necesitan configuración")
        
        if not results['aws_credentials']:
            print("💡 Configurar AWS CLI con perfil IA")
        if not results['llm']:
            print("💡 Verificar permisos de Bedrock para modelos LLM")
        if not results['embeddings']:
            print("💡 Verificar permisos de Bedrock para embeddings")


if __name__ == "__main__":
    asyncio.run(main())
