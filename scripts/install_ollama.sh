#!/bin/bash

# Script para instalar Ollama y modelos recomendados
# Uso: ./scripts/install_ollama.sh

set -e

echo "🚀 Instalando Ollama y modelos locales..."

# Detectar sistema operativo
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
else
    echo "❌ Sistema operativo no soportado: $OSTYPE"
    exit 1
fi

echo "📋 Sistema detectado: $OS"

# Instalar Ollama
echo "📦 Instalando Ollama..."
if command -v ollama &> /dev/null; then
    echo "✅ Ollama ya está instalado"
    ollama --version
else
    if [[ "$OS" == "linux" ]]; then
        curl -fsSL https://ollama.ai/install.sh | sh
    elif [[ "$OS" == "macos" ]]; then
        echo "📱 Para macOS, descarga Ollama desde: https://ollama.ai/download"
        echo "   O instala con Homebrew: brew install ollama"
        exit 1
    fi
fi

# Verificar instalación
if ! command -v ollama &> /dev/null; then
    echo "❌ Error: Ollama no se instaló correctamente"
    exit 1
fi

echo "✅ Ollama instalado correctamente"

# Iniciar servicio Ollama
echo "🔄 Iniciando servicio Ollama..."
if [[ "$OS" == "linux" ]]; then
    # En Linux, Ollama se ejecuta como servicio
    sudo systemctl start ollama || echo "⚠️  No se pudo iniciar como servicio, ejecutando manualmente..."
    
    # Si no funciona como servicio, ejecutar en background
    if ! pgrep -f "ollama serve" > /dev/null; then
        echo "🔧 Ejecutando Ollama en background..."
        nohup ollama serve > /tmp/ollama.log 2>&1 &
        sleep 3
    fi
elif [[ "$OS" == "macos" ]]; then
    # En macOS, ejecutar en background
    if ! pgrep -f "ollama serve" > /dev/null; then
        echo "🔧 Ejecutando Ollama en background..."
        nohup ollama serve > /tmp/ollama.log 2>&1 &
        sleep 3
    fi
fi

# Verificar que Ollama esté ejecutándose
echo "🔍 Verificando que Ollama esté ejecutándose..."
sleep 2

if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "❌ Error: Ollama no está respondiendo en localhost:11434"
    echo "💡 Intenta ejecutar manualmente: ollama serve"
    exit 1
fi

echo "✅ Ollama está ejecutándose correctamente"

# Descargar modelos recomendados
echo "📥 Descargando modelos recomendados..."

MODELS=("llama3.2:3b" "llama3.2:1b")

for model in "${MODELS[@]}"; do
    echo "📦 Descargando $model..."
    if ollama list | grep -q "$model"; then
        echo "✅ $model ya está descargado"
    else
        echo "⬇️  Descargando $model (esto puede tomar varios minutos)..."
        ollama pull "$model"
        if [ $? -eq 0 ]; then
            echo "✅ $model descargado correctamente"
        else
            echo "⚠️  Error descargando $model, continuando..."
        fi
    fi
done

# Probar modelo
echo "🧪 Probando modelo..."
echo "Pregunta de prueba: ¿Qué es la inteligencia artificial?"
ollama run llama3.2:3b "¿Qué es la inteligencia artificial? Responde en español en máximo 50 palabras."

echo ""
echo "🎉 ¡Instalación completada!"
echo ""
echo "📋 Modelos disponibles:"
ollama list
echo ""
echo "💡 Para usar Ollama:"
echo "   - Ejecutar modelo: ollama run llama3.2:3b"
echo "   - Listar modelos: ollama list"
echo "   - Detener servicio: pkill ollama"
echo ""
echo "🔗 El agente LightRAG detectará automáticamente Ollama y lo usará si no hay API key de OpenAI"
