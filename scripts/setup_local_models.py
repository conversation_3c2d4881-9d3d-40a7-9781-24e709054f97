#!/usr/bin/env python3
"""
Script para configurar modelos locales para el agente LightRAG
"""
import os
import sys
import subprocess
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent.parent))

def check_dependencies():
    """Verifica que las dependencias estén instaladas"""
    print("🔍 Verificando dependencias...")
    
    required_packages = [
        "sentence_transformers",
        "transformers", 
        "torch",
        "ollama"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Instalando paquetes faltantes: {', '.join(missing_packages)}")
        
        # Activar conda si está disponible
        conda_cmd = "source ~/miniconda3/bin/activate && python -m pip install"
        pip_cmd = "python -m pip install"
        
        cmd = conda_cmd if os.path.exists(os.path.expanduser("~/miniconda3")) else pip_cmd
        
        for package in missing_packages:
            print(f"📦 Instalando {package}...")
            result = subprocess.run(f"{cmd} {package}", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {package} instalado")
            else:
                print(f"❌ Error instalando {package}: {result.stderr}")
                return False
    
    return True

def download_sentence_transformers_models():
    """Descarga modelos de sentence-transformers"""
    print("\n📥 Descargando modelos de sentence-transformers...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        models = [
            "all-MiniLM-L6-v2",
            "all-mpnet-base-v2"
        ]
        
        cache_dir = Path("agent/lightrag/working_dir/sentence_transformers_cache")
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        for model_name in models:
            print(f"📦 Descargando {model_name}...")
            try:
                model = SentenceTransformer(model_name, cache_folder=str(cache_dir))
                print(f"✅ {model_name} descargado")
                
                # Probar el modelo
                test_embedding = model.encode(["Esto es una prueba"])
                print(f"🧪 Prueba exitosa: embedding de dimensión {len(test_embedding[0])}")
                
            except Exception as e:
                print(f"❌ Error descargando {model_name}: {e}")
        
        return True
        
    except ImportError:
        print("❌ sentence-transformers no está instalado")
        return False

def check_ollama():
    """Verifica si Ollama está disponible"""
    print("\n🔍 Verificando Ollama...")
    
    try:
        import ollama
        
        # Intentar listar modelos
        models = ollama.list()
        print(f"✅ Ollama disponible con {len(models.get('models', []))} modelos")
        
        if models.get('models'):
            print("📋 Modelos disponibles:")
            for model in models['models']:
                print(f"   - {model['name']}")
        else:
            print("⚠️  No hay modelos descargados en Ollama")
            print("💡 Ejecuta: ./scripts/install_ollama.sh")
        
        return True
        
    except Exception as e:
        print(f"❌ Ollama no disponible: {e}")
        print("💡 Instala Ollama ejecutando: ./scripts/install_ollama.sh")
        return False

def test_agent_config():
    """Prueba la configuración del agente"""
    print("\n🧪 Probando configuración del agente...")
    
    try:
        from agent.config import AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG
        
        print("📋 Modelos disponibles:")
        for provider, available in AVAILABLE_MODELS.items():
            status = "✅" if available else "❌"
            print(f"   {status} {provider}")
        
        print(f"\n🤖 LLM configurado:")
        print(f"   Proveedor: {LLM_CONFIG['provider']}")
        print(f"   Modelo: {LLM_CONFIG['model']}")
        
        print(f"\n🔤 Embeddings configurados:")
        print(f"   Proveedor: {EMBEDDING_CONFIG['provider']}")
        print(f"   Modelo: {EMBEDDING_CONFIG['model']}")
        print(f"   Dimensiones: {EMBEDDING_CONFIG['dimensions']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        return False

def main():
    """Función principal"""
    print("🚀 Configurando modelos locales para LightRAG Agent\n")
    
    # Verificar dependencias
    if not check_dependencies():
        print("❌ Error en dependencias")
        return 1
    
    # Descargar modelos de sentence-transformers
    if not download_sentence_transformers_models():
        print("⚠️  Error descargando modelos de sentence-transformers")
    
    # Verificar Ollama
    ollama_available = check_ollama()
    
    # Probar configuración
    if not test_agent_config():
        print("❌ Error en configuración del agente")
        return 1
    
    print("\n🎉 ¡Configuración completada!")
    print("\n📋 Resumen:")
    print("✅ Dependencias instaladas")
    print("✅ Modelos de sentence-transformers descargados")
    print(f"{'✅' if ollama_available else '⚠️ '} Ollama {'disponible' if ollama_available else 'no disponible'}")
    print("✅ Configuración del agente verificada")
    
    print("\n💡 Próximos pasos:")
    print("1. Ejecutar pruebas: python test_agent.py")
    print("2. Modo interactivo: python -m agent.main")
    
    if not ollama_available:
        print("3. Instalar Ollama: ./scripts/install_ollama.sh")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
