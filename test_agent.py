#!/usr/bin/env python3
"""
Script de prueba para el agente LightRAG con soporte para modelos locales
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))

from agent.main import initialize_agent, query_agent


async def test_basic_functionality():
    """
    Prueba la funcionalidad básica del agente
    """
    print("=== Prueba del Agente LightRAG ===\n")
    
    # Verificar configuración de modelos
    print("1. Verificando configuración...")
    
    try:
        from agent.config import AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG
        
        print(f"🤖 LLM: {LLM_CONFIG['provider']} - {LLM_CONFIG['model']}")
        print(f"🔤 Embeddings: {EMBEDDING_CONFIG['provider']} - {EMBEDDING_CONFIG['model']}")
        
        if AVAILABLE_MODELS["openai"]:
            print("✅ OpenAI disponible")
        elif AVAILABLE_MODELS["ollama"]:
            print("✅ Ollama disponible")
        elif AVAILABLE_MODELS["sentence_transformers"]:
            print("✅ Modelos locales disponibles")
        else:
            print("⚠️  No hay modelos disponibles")
            
    except Exception as e:
        print(f"❌ Error verificando configuración: {e}")
        return False
    
    # Verificar directorio de documentos
    docs_dir = Path("docs")
    if docs_dir.exists():
        doc_files = list(docs_dir.rglob("*.md")) + list(docs_dir.rglob("*.txt"))
        print(f"✅ Directorio docs encontrado con {len(doc_files)} archivos")
    else:
        print("⚠️  Directorio docs no encontrado")
    
    print("\n2. Inicializando agente...")
    
    try:
        # Inicializar agente
        success = await initialize_agent()
        
        if success:
            print("✅ Agente inicializado correctamente")
        else:
            print("❌ Error inicializando agente")
            return False
        
        print("\n3. Probando consultas...")
        
        # Pruebas de consulta
        test_questions = [
            "¿Qué es este agente?",
            "¿Cómo funciona LightRAG?",
            "¿Cuáles son las funcionalidades principales?",
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\nPregunta {i}: {question}")
            
            try:
                response = await query_agent(question, "hybrid")
                print(f"Respuesta: {response[:200]}...")
                print("✅ Consulta exitosa")
                
            except Exception as e:
                print(f"❌ Error en consulta: {e}")
        
        print("\n=== Pruebas completadas ===")
        return True
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        return False


async def test_different_modes():
    """
    Prueba diferentes modos de consulta
    """
    print("\n=== Prueba de Modos de Consulta ===\n")
    
    question = "¿Qué es la arquitectura del agente?"
    modes = ["naive", "local", "global", "hybrid"]
    
    for mode in modes:
        print(f"Probando modo '{mode}'...")
        
        try:
            response = await query_agent(question, mode)
            print(f"✅ Modo {mode}: {len(response)} caracteres de respuesta")
            
        except Exception as e:
            print(f"❌ Error en modo {mode}: {e}")


def test_model_availability():
    """
    Prueba la disponibilidad de diferentes modelos
    """
    print("\n=== Prueba de Disponibilidad de Modelos ===\n")
    
    try:
        from agent.config import AVAILABLE_MODELS, LOCAL_MODEL_CONFIG
        
        print("📋 Estado de modelos:")
        for provider, available in AVAILABLE_MODELS.items():
            status = "✅" if available else "❌"
            print(f"   {status} {provider}")
        
        # Probar sentence-transformers
        if AVAILABLE_MODELS["sentence_transformers"]:
            print("\n🔤 Probando sentence-transformers...")
            try:
                from sentence_transformers import SentenceTransformer
                model = SentenceTransformer("all-MiniLM-L6-v2")
                test_embedding = model.encode(["Prueba de embedding"])
                print(f"✅ Embedding generado: dimensión {len(test_embedding[0])}")
            except Exception as e:
                print(f"❌ Error con sentence-transformers: {e}")
        
        # Probar Ollama
        if AVAILABLE_MODELS["ollama"]:
            print("\n🦙 Probando Ollama...")
            try:
                import ollama
                models = ollama.list()
                print(f"✅ Ollama disponible con {len(models.get('models', []))} modelos")
                
                if models.get('models'):
                    print("📋 Modelos en Ollama:")
                    for model in models['models'][:3]:  # Mostrar solo los primeros 3
                        print(f"   - {model['name']}")
            except Exception as e:
                print(f"❌ Error con Ollama: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error probando modelos: {e}")
        return False


def create_sample_documents():
    """
    Crea documentos de ejemplo si no existen
    """
    docs_dir = Path("docs")
    
    if not docs_dir.exists():
        print("Creando documentos de ejemplo...")
        docs_dir.mkdir(exist_ok=True)
        
        # Ya tenemos algunos documentos creados anteriormente
        print("✅ Documentos de ejemplo disponibles")
    else:
        print("✅ Directorio de documentos ya existe")


async def main():
    """Función principal de pruebas"""
    print("🚀 Iniciando pruebas del agente LightRAG...\n")
    
    # Crear documentos de ejemplo si es necesario
    create_sample_documents()
    
    # Probar disponibilidad de modelos
    model_test = test_model_availability()
    
    if not model_test:
        print("\n⚠️  Problemas con modelos, pero continuando con pruebas básicas...")
    
    # Ejecutar pruebas básicas
    basic_success = await test_basic_functionality()
    
    if basic_success:
        # Ejecutar pruebas de modos
        await test_different_modes()
    
    print("\n🎉 Pruebas finalizadas")
    
    # Mostrar recomendaciones
    print("\n💡 Recomendaciones:")
    print("1. Para mejor rendimiento, instala Ollama: ./scripts/install_ollama.sh")
    print("2. Para usar OpenAI, configura OPENAI_API_KEY en .env")
    print("3. Ejecuta modo interactivo: python -m agent.main")


if __name__ == "__main__":
    asyncio.run(main())
