#!/usr/bin/env python3
"""
Test directo del RAG con modelos Hugging Face (sin dependencias problemáticas)
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


async def test_configuration():
    """Prueba la configuración básica"""
    print("🔧 Probando configuración...")
    
    try:
        # Importar solo la configuración, sin el agente problemático
        from agent.config.lightrag_config import (
            LLM_CONFIG, EMBEDDING_CONFIG, AVAILABLE_MODELS, LOCAL_MODEL_CONFIG
        )
        
        print(f"✅ LLM Provider: {LLM_CONFIG['provider']}")
        print(f"✅ LLM Model: {LLM_CONFIG['model']}")
        print(f"✅ Embedding Provider: {EMBEDDING_CONFIG['provider']}")
        print(f"✅ Embedding Model: {EMBEDDING_CONFIG['model']}")
        
        print(f"✅ Modelos disponibles: {AVAILABLE_MODELS}")
        
        # Verificar que transformers sea el proveedor
        assert LLM_CONFIG['provider'] == 'transformers', f"Expected transformers, got {LLM_CONFIG['provider']}"
        print("✅ Hugging Face transformers configurado como proveedor primario")
        
        return True, LLM_CONFIG, EMBEDDING_CONFIG
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None


async def test_sentence_transformers():
    """Prueba sentence-transformers directamente"""
    print("\n🔤 Probando sentence-transformers...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # Cargar modelo
        model = SentenceTransformer("all-MiniLM-L6-v2")
        
        # Probar embeddings
        texts = [
            "¿Qué es LightRAG?",
            "LightRAG es un framework para RAG",
            "Los modelos Hugging Face son gratuitos"
        ]
        
        embeddings = model.encode(texts)
        
        print(f"✅ Modelo cargado: all-MiniLM-L6-v2")
        print(f"✅ Embeddings generados: {len(embeddings)} textos")
        print(f"✅ Dimensiones: {len(embeddings[0])}")
        
        # Calcular similitud
        from sklearn.metrics.pairwise import cosine_similarity
        similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        print(f"✅ Similitud entre textos relacionados: {similarity:.3f}")
        
        return model, embeddings
        
    except Exception as e:
        print(f"❌ Error con sentence-transformers: {e}")
        import traceback
        traceback.print_exc()
        return None, None


async def test_transformers_llm():
    """Prueba el modelo de generación de Hugging Face"""
    print("\n🤖 Probando modelo de generación...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        model_name = "microsoft/DialoGPT-medium"
        
        print(f"📦 Cargando tokenizer: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(model_name, padding_side="left")
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print(f"📦 Cargando modelo: {model_name}")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
            low_cpu_mem_usage=True
        )
        
        if torch.cuda.is_available():
            model = model.cuda()
            print("✅ Modelo movido a GPU")
        
        # Probar generación
        prompt = "User: ¿Qué es la inteligencia artificial?\nBot:"
        
        inputs = tokenizer.encode(prompt, return_tensors="pt", max_length=512, truncation=True)
        
        if torch.cuda.is_available():
            inputs = inputs.cuda()
        
        print("🔄 Generando respuesta...")
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_new_tokens=100,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1,
                no_repeat_ngram_size=3
            )
        
        # Decodificar respuesta
        new_tokens = outputs[0][inputs.shape[1]:]
        response = tokenizer.decode(new_tokens, skip_special_tokens=True)
        
        print(f"✅ Respuesta generada: {response}")
        
        return tokenizer, model
        
    except Exception as e:
        print(f"❌ Error con modelo de generación: {e}")
        import traceback
        traceback.print_exc()
        return None, None


async def test_vector_search(embedding_model):
    """Prueba búsqueda vectorial"""
    print("\n🔍 Probando búsqueda vectorial...")
    
    try:
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        # Documentos de prueba
        documents = [
            "LightRAG es un framework avanzado para Retrieval-Augmented Generation que combina búsqueda vectorial y de grafos.",
            "Los modelos de Hugging Face permiten ejecutar IA localmente sin APIs pagadas.",
            "La búsqueda semántica utiliza embeddings para encontrar contenido relevante por significado.",
            "Los transformers son una arquitectura de red neuronal muy efectiva para procesamiento de lenguaje natural.",
            "El agente RAG puede responder preguntas basándose en documentos indexados."
        ]
        
        # Generar embeddings de documentos
        doc_embeddings = embedding_model.encode(documents)
        
        # Consulta de prueba
        query = "¿Cómo funciona la búsqueda semántica en LightRAG?"
        query_embedding = embedding_model.encode([query])
        
        # Calcular similitudes
        similarities = cosine_similarity(query_embedding, doc_embeddings)[0]
        
        # Encontrar documentos más relevantes
        top_k = 3
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        
        print(f"✅ Consulta: {query}")
        print(f"✅ Documentos más relevantes:")
        
        for i, idx in enumerate(top_indices):
            score = similarities[idx]
            doc = documents[idx]
            print(f"   {i+1}. Score: {score:.3f} - {doc[:60]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en búsqueda vectorial: {e}")
        return False


async def test_rag_pipeline(embedding_model, tokenizer, llm_model):
    """Prueba el pipeline RAG completo"""
    print("\n🔄 Probando pipeline RAG completo...")
    
    try:
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        import torch
        
        # Base de conocimiento
        knowledge_base = [
            "LightRAG es un framework para Retrieval-Augmented Generation que combina búsqueda vectorial y de grafos para proporcionar respuestas contextuales.",
            "Los modelos de Hugging Face como sentence-transformers y transformers permiten ejecutar IA localmente sin depender de APIs pagadas.",
            "La búsqueda semántica utiliza embeddings vectoriales para encontrar documentos relevantes basándose en el significado, no solo en palabras clave.",
            "El agente RAG procesa documentos, los indexa usando embeddings, y luego usa esa información para generar respuestas contextuales a las consultas."
        ]
        
        # Indexar documentos
        doc_embeddings = embedding_model.encode(knowledge_base)
        
        # Consulta del usuario
        user_query = "¿Qué ventajas tienen los modelos locales de Hugging Face?"
        
        # 1. Búsqueda de documentos relevantes
        query_embedding = embedding_model.encode([user_query])
        similarities = cosine_similarity(query_embedding, doc_embeddings)[0]
        
        # Obtener top-2 documentos
        top_indices = np.argsort(similarities)[-2:][::-1]
        relevant_docs = [knowledge_base[i] for i in top_indices if similarities[i] > 0.1]
        
        print(f"✅ Consulta: {user_query}")
        print(f"✅ Documentos relevantes encontrados: {len(relevant_docs)}")
        
        # 2. Construir contexto
        context = "\n".join(relevant_docs)
        
        # 3. Generar respuesta
        prompt = f"""Basándote en el siguiente contexto, responde la pregunta en español:

Contexto:
{context}

Pregunta: {user_query}
Respuesta:"""
        
        inputs = tokenizer.encode(prompt, return_tensors="pt", max_length=512, truncation=True)
        
        if torch.cuda.is_available():
            inputs = inputs.cuda()
        
        with torch.no_grad():
            outputs = llm_model.generate(
                inputs,
                max_new_tokens=150,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        new_tokens = outputs[0][inputs.shape[1]:]
        response = tokenizer.decode(new_tokens, skip_special_tokens=True)
        
        print(f"✅ Respuesta RAG: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en pipeline RAG: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_document_processing():
    """Prueba el procesamiento de documentos reales"""
    print("\n📚 Probando procesamiento de documentos...")
    
    try:
        # Verificar si existen documentos
        docs_dir = Path("docs")
        if not docs_dir.exists():
            print("📁 Creando documentos de prueba...")
            docs_dir.mkdir(exist_ok=True)
            
            # Crear documento de prueba
            test_doc = docs_dir / "lightrag_info.md"
            test_doc.write_text("""
# LightRAG Framework

## Introducción
LightRAG es un framework avanzado para Retrieval-Augmented Generation que revoluciona la forma en que los sistemas de IA acceden y utilizan información.

## Características Principales
- **Búsqueda Vectorial**: Utiliza embeddings para búsqueda semántica
- **Grafo de Conocimiento**: Mantiene relaciones entre conceptos
- **Modelos Locales**: Soporte para Hugging Face y Ollama
- **Múltiples Modos**: naive, local, global, hybrid

## Ventajas de Modelos Locales
Los modelos de Hugging Face ofrecen:
1. Ejecución completamente local
2. Sin dependencia de APIs pagadas
3. Control total sobre los datos
4. Privacidad garantizada
5. Costos predecibles

## Arquitectura
El sistema combina:
- Sentence-transformers para embeddings
- Transformers para generación
- ChromaDB para almacenamiento vectorial
- Búsqueda híbrida para mejores resultados
""")
        
        # Leer documentos
        doc_files = list(docs_dir.rglob("*.md")) + list(docs_dir.rglob("*.txt"))
        documents = []
        
        for file_path in doc_files:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                documents.append({
                    'text': content,
                    'source': str(file_path)
                })
        
        print(f"✅ Procesados {len(documents)} documentos")
        
        # Mostrar contenido
        for i, doc in enumerate(documents[:2]):  # Solo primeros 2
            print(f"   Doc {i+1}: {doc['source']} ({len(doc['text'])} chars)")
        
        return documents
        
    except Exception as e:
        print(f"❌ Error procesando documentos: {e}")
        return []


async def main():
    """Función principal"""
    print("🚀 Test completo de RAG con modelos Hugging Face\n")
    
    results = {}
    
    # 1. Configuración
    config_ok, llm_config, embedding_config = await test_configuration()
    results['configuration'] = config_ok
    
    if not config_ok:
        print("❌ Configuración falló, abortando")
        return
    
    # 2. Sentence-transformers
    embedding_model, embeddings = await test_sentence_transformers()
    results['embeddings'] = embedding_model is not None
    
    # 3. Modelo de generación
    tokenizer, llm_model = await test_transformers_llm()
    results['llm'] = tokenizer is not None and llm_model is not None
    
    # 4. Búsqueda vectorial
    if embedding_model:
        results['vector_search'] = await test_vector_search(embedding_model)
    else:
        results['vector_search'] = False
    
    # 5. Pipeline RAG completo
    if embedding_model and tokenizer and llm_model:
        results['rag_pipeline'] = await test_rag_pipeline(embedding_model, tokenizer, llm_model)
    else:
        results['rag_pipeline'] = False
    
    # 6. Procesamiento de documentos
    documents = await test_document_processing()
    results['document_processing'] = len(documents) > 0
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests >= 4:
        print("\n🎉 ¡RAG con Hugging Face funcionando correctamente!")
        print("\n✅ Funcionalidades verificadas:")
        print("- Configuración con transformers como proveedor primario")
        print("- Embeddings locales con sentence-transformers")
        print("- Generación de texto con DialoGPT")
        print("- Búsqueda vectorial semántica")
        print("- Pipeline RAG completo")
        print("- Procesamiento de documentos")
        
        print("\n🚀 El agente está listo para:")
        print("- Procesar documentos localmente")
        print("- Realizar búsquedas semánticas")
        print("- Generar respuestas contextuales")
        print("- Funcionar sin APIs pagadas")
        
    else:
        print("⚠️  Algunas funcionalidades necesitan ajustes")
        
        if not results['embeddings']:
            print("💡 Instalar: pip install sentence-transformers")
        if not results['llm']:
            print("💡 Instalar: pip install transformers torch")


if __name__ == "__main__":
    asyncio.run(main())
