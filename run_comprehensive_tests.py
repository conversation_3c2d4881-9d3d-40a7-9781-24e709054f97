#!/usr/bin/env python3
"""
Script completo para ejecutar todas las pruebas del agente LightRAG con Hugging Face
"""
import asyncio
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Ejecuta un comando y muestra el resultado"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"Comando: {command}")
    print("-" * 60)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.returncode == 0:
            print("✅ ÉXITO")
            print(result.stdout)
        else:
            print("❌ ERROR")
            print(result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ EXCEPCIÓN: {e}")
        return False


async def main():
    """Función principal"""
    print("🎯 PRUEBAS COMPLETAS DEL AGENTE LIGHTRAG CON HUGGING FACE")
    print("=" * 80)
    
    # Lista de pruebas a ejecutar
    tests = [
        {
            "command": "source ~/miniconda3/bin/activate && python test_local_models.py",
            "description": "Prueba de modelos locales básicos"
        },
        {
            "command": "source ~/miniconda3/bin/activate && python test_huggingface_rag.py",
            "description": "Prueba completa de RAG con Hugging Face"
        },
        {
            "command": "source ~/miniconda3/bin/activate && python test_adk_integration.py",
            "description": "Prueba de integración con Google ADK"
        }
    ]
    
    results = []
    
    for i, test in enumerate(tests, 1):
        print(f"\n🔄 EJECUTANDO PRUEBA {i}/{len(tests)}")
        success = run_command(test["command"], test["description"])
        results.append({
            "test": test["description"],
            "success": success
        })
    
    # Resumen final
    print(f"\n{'='*80}")
    print("📊 RESUMEN FINAL DE PRUEBAS")
    print(f"{'='*80}")
    
    successful_tests = 0
    for i, result in enumerate(results, 1):
        status = "✅" if result["success"] else "❌"
        print(f"{i}. {status} {result['test']}")
        if result["success"]:
            successful_tests += 1
    
    total_tests = len(results)
    print(f"\n🎯 RESULTADO FINAL: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests == total_tests:
        print("\n🎉 ¡TODAS LAS PRUEBAS EXITOSAS!")
        print("\n✅ FUNCIONALIDADES VERIFICADAS:")
        print("   - Configuración con Hugging Face como proveedor primario")
        print("   - Embeddings locales con sentence-transformers")
        print("   - Generación de texto con DialoGPT")
        print("   - Búsqueda vectorial semántica")
        print("   - Pipeline RAG completo")
        print("   - Integración con Google ADK")
        print("   - Procesamiento de documentos")
        print("   - Herramientas ADK (LightRAG, DocumentLoader, KnowledgeGraph)")
        
        print("\n🚀 COMANDOS PARA USAR EL AGENTE:")
        print("   1. Prueba rápida de modelos:")
        print("      python test_local_models.py")
        print("   2. Prueba completa de RAG:")
        print("      python test_huggingface_rag.py")
        print("   3. Prueba de integración ADK:")
        print("      python test_adk_integration.py")
        
        print("\n💡 EJEMPLO DE USO DIRECTO:")
        print("""
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModelForCausalLM

# Cargar modelos
embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-medium")
llm_model = AutoModelForCausalLM.from_pretrained("microsoft/DialoGPT-medium")

# Usar para RAG
query = "¿Qué es LightRAG?"
# ... (ver test_huggingface_rag.py para ejemplo completo)
""")
        
        print("\n🔧 CONFIGURACIÓN VERIFICADA:")
        print("   - LLM Provider: transformers (Hugging Face)")
        print("   - LLM Model: microsoft/DialoGPT-medium")
        print("   - Embedding Provider: sentence_transformers")
        print("   - Embedding Model: all-MiniLM-L6-v2")
        print("   - Vector Database: En memoria con sklearn")
        print("   - Graph Database: Simulado con relaciones vectoriales")
        
    elif successful_tests >= 2:
        print("\n⚠️  MAYORÍA DE PRUEBAS EXITOSAS")
        print("   El sistema funciona pero algunas características necesitan ajustes")
        
    else:
        print("\n❌ MÚLTIPLES FALLOS")
        print("   Revisar configuración y dependencias")
        
        print("\n🔧 SOLUCIONES COMUNES:")
        print("   1. Instalar dependencias:")
        print("      pip install sentence-transformers transformers torch accelerate")
        print("   2. Verificar memoria disponible (modelos requieren ~2GB RAM)")
        print("   3. Verificar permisos de escritura en directorio de trabajo")


if __name__ == "__main__":
    asyncio.run(main())
