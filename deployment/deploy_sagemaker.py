#!/usr/bin/env python3
"""
Script para desplegar el agente en AWS SageMaker
"""
import boto3
import json
import time
from datetime import datetime
from pathlib import Path
import argparse
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SageMakerDeployer:
    """
    Clase para manejar el despliegue en SageMaker
    """
    
    def __init__(self, profile_name="IA", region="us-east-1"):
        """
        Inicializa el deployer
        
        Args:
            profile_name: Nombre del perfil AWS
            region: Región de AWS
        """
        self.profile_name = profile_name
        self.region = region
        
        # Crear sesión con el perfil especificado
        self.session = boto3.Session(profile_name=profile_name)
        self.sagemaker = self.session.client('sagemaker', region_name=region)
        self.ecr = self.session.client('ecr', region_name=region)
        self.sts = self.session.client('sts', region_name=region)
        
        # Obtener información de la cuenta
        self.account_id = self.sts.get_caller_identity()['Account']
        
        logger.info(f"Inicializado para cuenta {self.account_id} en región {region}")
    
    def create_ecr_repository(self, repo_name="lightrag-agent"):
        """
        Crea repositorio ECR si no existe
        
        Args:
            repo_name: Nombre del repositorio
            
        Returns:
            str: URI del repositorio
        """
        try:
            # Verificar si el repositorio existe
            response = self.ecr.describe_repositories(repositoryNames=[repo_name])
            repo_uri = response['repositories'][0]['repositoryUri']
            logger.info(f"Repositorio ECR existente: {repo_uri}")
            
        except self.ecr.exceptions.RepositoryNotFoundException:
            # Crear repositorio
            logger.info(f"Creando repositorio ECR: {repo_name}")
            response = self.ecr.create_repository(repositoryName=repo_name)
            repo_uri = response['repository']['repositoryUri']
            logger.info(f"Repositorio ECR creado: {repo_uri}")
        
        return repo_uri
    
    def build_and_push_image(self, repo_uri, dockerfile_path="sagemaker/Dockerfile"):
        """
        Construye y sube la imagen Docker
        
        Args:
            repo_uri: URI del repositorio ECR
            dockerfile_path: Ruta al Dockerfile
            
        Returns:
            str: URI completa de la imagen
        """
        import subprocess
        
        # Obtener token de autenticación
        token_response = self.ecr.get_authorization_token()
        token = token_response['authorizationData'][0]['authorizationToken']
        endpoint = token_response['authorizationData'][0]['proxyEndpoint']
        
        # Decodificar token
        import base64
        username, password = base64.b64decode(token).decode().split(':')
        
        # Login a ECR
        login_cmd = f"echo {password} | docker login --username {username} --password-stdin {endpoint}"
        subprocess.run(login_cmd, shell=True, check=True)
        
        # Construir imagen
        tag = f"{repo_uri}:latest"
        build_cmd = f"docker build -f {dockerfile_path} -t {tag} ."
        
        logger.info(f"Construyendo imagen: {build_cmd}")
        subprocess.run(build_cmd, shell=True, check=True)
        
        # Subir imagen
        push_cmd = f"docker push {tag}"
        logger.info(f"Subiendo imagen: {push_cmd}")
        subprocess.run(push_cmd, shell=True, check=True)
        
        return tag
    
    def create_model(self, model_name, image_uri, role_arn):
        """
        Crea modelo en SageMaker
        
        Args:
            model_name: Nombre del modelo
            image_uri: URI de la imagen Docker
            role_arn: ARN del rol de ejecución
            
        Returns:
            str: ARN del modelo
        """
        try:
            response = self.sagemaker.create_model(
                ModelName=model_name,
                PrimaryContainer={
                    'Image': image_uri,
                    'Mode': 'SingleModel'
                },
                ExecutionRoleArn=role_arn
            )
            
            model_arn = response['ModelArn']
            logger.info(f"Modelo creado: {model_arn}")
            return model_arn
            
        except Exception as e:
            logger.error(f"Error creando modelo: {e}")
            raise
    
    def create_endpoint_config(self, config_name, model_name, instance_type="ml.t3.medium"):
        """
        Crea configuración de endpoint
        
        Args:
            config_name: Nombre de la configuración
            model_name: Nombre del modelo
            instance_type: Tipo de instancia
            
        Returns:
            str: ARN de la configuración
        """
        try:
            response = self.sagemaker.create_endpoint_config(
                EndpointConfigName=config_name,
                ProductionVariants=[
                    {
                        'VariantName': 'primary',
                        'ModelName': model_name,
                        'InitialInstanceCount': 1,
                        'InstanceType': instance_type,
                        'InitialVariantWeight': 1.0
                    }
                ]
            )
            
            config_arn = response['EndpointConfigArn']
            logger.info(f"Configuración de endpoint creada: {config_arn}")
            return config_arn
            
        except Exception as e:
            logger.error(f"Error creando configuración de endpoint: {e}")
            raise
    
    def create_endpoint(self, endpoint_name, config_name):
        """
        Crea endpoint
        
        Args:
            endpoint_name: Nombre del endpoint
            config_name: Nombre de la configuración
            
        Returns:
            str: ARN del endpoint
        """
        try:
            response = self.sagemaker.create_endpoint(
                EndpointName=endpoint_name,
                EndpointConfigName=config_name
            )
            
            endpoint_arn = response['EndpointArn']
            logger.info(f"Endpoint creado: {endpoint_arn}")
            
            # Esperar a que el endpoint esté en servicio
            self.wait_for_endpoint(endpoint_name)
            
            return endpoint_arn
            
        except Exception as e:
            logger.error(f"Error creando endpoint: {e}")
            raise
    
    def wait_for_endpoint(self, endpoint_name, max_wait_time=1800):
        """
        Espera a que el endpoint esté en servicio
        
        Args:
            endpoint_name: Nombre del endpoint
            max_wait_time: Tiempo máximo de espera en segundos
        """
        logger.info(f"Esperando a que el endpoint {endpoint_name} esté en servicio...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            response = self.sagemaker.describe_endpoint(EndpointName=endpoint_name)
            status = response['EndpointStatus']
            
            logger.info(f"Estado del endpoint: {status}")
            
            if status == 'InService':
                logger.info("¡Endpoint en servicio!")
                return
            elif status == 'Failed':
                raise Exception(f"Endpoint falló: {response.get('FailureReason', 'Unknown')}")
            
            time.sleep(30)
        
        raise Exception(f"Timeout esperando endpoint después de {max_wait_time} segundos")
    
    def deploy_complete_stack(self, role_arn, instance_type="ml.t3.medium"):
        """
        Despliega el stack completo
        
        Args:
            role_arn: ARN del rol de ejecución
            instance_type: Tipo de instancia
            
        Returns:
            dict: Información del despliegue
        """
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        
        # Nombres únicos
        repo_name = "lightrag-agent"
        model_name = f"lightrag-agent-{timestamp}"
        config_name = f"lightrag-agent-config-{timestamp}"
        endpoint_name = f"lightrag-agent-endpoint-{timestamp}"
        
        try:
            # 1. Crear repositorio ECR
            repo_uri = self.create_ecr_repository(repo_name)
            
            # 2. Construir y subir imagen
            image_uri = self.build_and_push_image(repo_uri)
            
            # 3. Crear modelo
            model_arn = self.create_model(model_name, image_uri, role_arn)
            
            # 4. Crear configuración de endpoint
            config_arn = self.create_endpoint_config(config_name, model_name, instance_type)
            
            # 5. Crear endpoint
            endpoint_arn = self.create_endpoint(endpoint_name, config_name)
            
            deployment_info = {
                "timestamp": timestamp,
                "repo_uri": repo_uri,
                "image_uri": image_uri,
                "model_name": model_name,
                "model_arn": model_arn,
                "config_name": config_name,
                "config_arn": config_arn,
                "endpoint_name": endpoint_name,
                "endpoint_arn": endpoint_arn,
                "region": self.region,
                "account_id": self.account_id
            }
            
            # Guardar información del despliegue
            with open(f"deployment_info_{timestamp}.json", "w") as f:
                json.dump(deployment_info, f, indent=2)
            
            logger.info("¡Despliegue completado exitosamente!")
            logger.info(f"Endpoint: {endpoint_name}")
            logger.info(f"Región: {self.region}")
            
            return deployment_info
            
        except Exception as e:
            logger.error(f"Error en despliegue: {e}")
            raise


def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description="Despliega agente LightRAG en SageMaker")
    parser.add_argument("--profile", default="IA", help="Perfil AWS")
    parser.add_argument("--region", default="us-east-1", help="Región AWS")
    parser.add_argument("--role-arn", required=True, help="ARN del rol de ejecución")
    parser.add_argument("--instance-type", default="ml.t3.medium", help="Tipo de instancia")
    
    args = parser.parse_args()
    
    # Crear deployer
    deployer = SageMakerDeployer(args.profile, args.region)
    
    # Desplegar
    deployment_info = deployer.deploy_complete_stack(
        args.role_arn,
        args.instance_type
    )
    
    print("\n=== Información del Despliegue ===")
    print(f"Endpoint: {deployment_info['endpoint_name']}")
    print(f"Región: {deployment_info['region']}")
    print(f"Archivo de info: deployment_info_{deployment_info['timestamp']}.json")


if __name__ == "__main__":
    main()
