#!/usr/bin/env python3
"""
Script para probar el endpoint de SageMaker
"""
import boto3
import json
import argparse
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EndpointTester:
    """
    Clase para probar endpoints de SageMaker
    """
    
    def __init__(self, profile_name="IA", region="us-east-1"):
        """
        Inicializa el tester
        
        Args:
            profile_name: Nombre del perfil AWS
            region: Región de AWS
        """
        self.profile_name = profile_name
        self.region = region
        
        # Crear sesión con el perfil especificado
        self.session = boto3.Session(profile_name=profile_name)
        self.runtime = self.session.client('sagemaker-runtime', region_name=region)
        
        logger.info(f"Inicializado para región {region}")
    
    def test_endpoint(self, endpoint_name, question, mode="hybrid", use_adk=False):
        """
        Prueba el endpoint con una consulta
        
        Args:
            endpoint_name: Nombre del endpoint
            question: Pregunta a realizar
            mode: Modo de búsqueda
            use_adk: Si usar integración ADK
            
        Returns:
            dict: Respuesta del endpoint
        """
        try:
            # Preparar payload
            payload = {
                "question": question,
                "mode": mode,
                "use_adk": use_adk
            }
            
            logger.info(f"Enviando consulta: {question}")
            logger.info(f"Modo: {mode}, ADK: {use_adk}")
            
            # Invocar endpoint
            response = self.runtime.invoke_endpoint(
                EndpointName=endpoint_name,
                ContentType='application/json',
                Body=json.dumps(payload)
            )
            
            # Procesar respuesta
            result = json.loads(response['Body'].read().decode())
            
            logger.info("Respuesta recibida exitosamente")
            return result
            
        except Exception as e:
            logger.error(f"Error probando endpoint: {e}")
            raise
    
    def test_health_check(self, endpoint_name):
        """
        Prueba el health check del endpoint
        
        Args:
            endpoint_name: Nombre del endpoint
            
        Returns:
            bool: True si el endpoint está saludable
        """
        try:
            # Usar una consulta simple para verificar salud
            result = self.test_endpoint(
                endpoint_name,
                "¿Estás funcionando?",
                "naive"
            )
            
            return "response" in result and result.get("response") is not None
            
        except Exception as e:
            logger.error(f"Health check falló: {e}")
            return False
    
    def run_test_suite(self, endpoint_name):
        """
        Ejecuta una suite completa de pruebas
        
        Args:
            endpoint_name: Nombre del endpoint
        """
        logger.info(f"Ejecutando suite de pruebas para {endpoint_name}")
        
        # Pruebas básicas
        test_cases = [
            {
                "question": "¿Qué es este agente?",
                "mode": "hybrid",
                "use_adk": False,
                "description": "Consulta básica sobre el agente"
            },
            {
                "question": "¿Cómo funciona LightRAG?",
                "mode": "local",
                "use_adk": False,
                "description": "Consulta técnica en modo local"
            },
            {
                "question": "¿Cuáles son las funcionalidades principales?",
                "mode": "global",
                "use_adk": False,
                "description": "Consulta general en modo global"
            },
            {
                "question": "Explica la arquitectura del sistema",
                "mode": "hybrid",
                "use_adk": True,
                "description": "Consulta con integración ADK"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n--- Prueba {i}: {test_case['description']} ---")
            
            try:
                result = self.test_endpoint(
                    endpoint_name,
                    test_case["question"],
                    test_case["mode"],
                    test_case["use_adk"]
                )
                
                # Verificar respuesta
                if "response" in result and result["response"]:
                    logger.info("✅ Prueba exitosa")
                    logger.info(f"Respuesta: {result['response'][:100]}...")
                    results.append({"test": i, "status": "success", "result": result})
                else:
                    logger.warning("⚠️ Respuesta vacía o inválida")
                    results.append({"test": i, "status": "warning", "result": result})
                
            except Exception as e:
                logger.error(f"❌ Prueba falló: {e}")
                results.append({"test": i, "status": "failed", "error": str(e)})
        
        # Resumen
        successful = len([r for r in results if r["status"] == "success"])
        total = len(results)
        
        logger.info(f"\n=== Resumen de Pruebas ===")
        logger.info(f"Exitosas: {successful}/{total}")
        logger.info(f"Endpoint: {endpoint_name}")
        
        return results


def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description="Prueba endpoint de SageMaker")
    parser.add_argument("--endpoint-name", required=True, help="Nombre del endpoint")
    parser.add_argument("--profile", default="IA", help="Perfil AWS")
    parser.add_argument("--region", default="us-east-1", help="Región AWS")
    parser.add_argument("--question", help="Pregunta específica a probar")
    parser.add_argument("--mode", default="hybrid", choices=["naive", "local", "global", "hybrid"], help="Modo de búsqueda")
    parser.add_argument("--use-adk", action="store_true", help="Usar integración ADK")
    parser.add_argument("--test-suite", action="store_true", help="Ejecutar suite completa de pruebas")
    
    args = parser.parse_args()
    
    # Crear tester
    tester = EndpointTester(args.profile, args.region)
    
    if args.test_suite:
        # Ejecutar suite completa
        results = tester.run_test_suite(args.endpoint_name)
        
        # Guardar resultados
        with open(f"test_results_{args.endpoint_name}.json", "w") as f:
            json.dump(results, f, indent=2)
        
    elif args.question:
        # Prueba individual
        result = tester.test_endpoint(
            args.endpoint_name,
            args.question,
            args.mode,
            args.use_adk
        )
        
        print("\n=== Resultado ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    else:
        # Health check
        healthy = tester.test_health_check(args.endpoint_name)
        
        if healthy:
            print(f"✅ Endpoint {args.endpoint_name} está funcionando correctamente")
        else:
            print(f"❌ Endpoint {args.endpoint_name} no está respondiendo correctamente")


if __name__ == "__main__":
    main()
