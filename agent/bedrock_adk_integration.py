"""
Integración de Google ADK con AWS Bedrock
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from pathlib import Path

from .bedrock_rag_agent import get_bedrock_agent


class BedrockLightRAGTool:
    """Herramienta ADK para consultas RAG usando Bedrock"""
    
    def __init__(self):
        self.name = "bedrock_lightrag_query"
        self.description = "Consulta documentos usando RAG con AWS Bedrock"
        self.bedrock_agent = get_bedrock_agent()
        self.logger = logging.getLogger(__name__)
        self.parameters = {
            "question": {
                "type": "string",
                "description": "Pregunta a realizar sobre los documentos",
                "required": True
            },
            "mode": {
                "type": "string", 
                "description": "Modo de búsqueda: naive, local, global, hybrid",
                "enum": ["naive", "local", "global", "hybrid"],
                "default": "hybrid"
            }
        }
    
    async def execute(self, question: str, mode: str = "hybrid") -> Dict[str, Any]:
        """Ejecuta consulta usando Bedrock"""
        try:
            self.logger.info(f"Ejecutando consulta Bedrock: {question}")
            
            response = await self.bedrock_agent.query(question, mode)
            
            return {
                "success": True,
                "response": response,
                "mode": mode,
                "question": question,
                "tool": self.name,
                "provider": "bedrock"
            }
            
        except Exception as e:
            self.logger.error(f"Error en BedrockLightRAGTool: {e}")
            return {
                "success": False,
                "error": str(e),
                "question": question,
                "tool": self.name
            }


class BedrockDocumentLoaderTool:
    """Herramienta ADK para cargar documentos en Bedrock"""
    
    def __init__(self):
        self.name = "bedrock_load_documents"
        self.description = "Carga documentos en la base de conocimiento usando Bedrock"
        self.bedrock_agent = get_bedrock_agent()
        self.logger = logging.getLogger(__name__)
        self.parameters = {
            "docs_path": {
                "type": "string",
                "description": "Ruta a los documentos a cargar (opcional)",
                "required": False
            }
        }
    
    async def execute(self, docs_path: Optional[str] = None) -> Dict[str, Any]:
        """Carga documentos usando Bedrock"""
        try:
            self.logger.info(f"Cargando documentos con Bedrock desde: {docs_path or 'directorio por defecto'}")
            
            path = Path(docs_path) if docs_path else None
            success = await self.bedrock_agent.load_documents(path)
            
            return {
                "success": success,
                "message": "Documentos cargados exitosamente con Bedrock" if success else "Error cargando documentos",
                "docs_path": docs_path or "directorio por defecto",
                "tool": self.name,
                "provider": "bedrock"
            }
            
        except Exception as e:
            self.logger.error(f"Error en BedrockDocumentLoaderTool: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": self.name
            }


class BedrockKnowledgeGraphTool:
    """Herramienta ADK para información del sistema Bedrock"""
    
    def __init__(self):
        self.name = "bedrock_knowledge_graph_info"
        self.description = "Obtiene información sobre el sistema Bedrock y la base de conocimiento"
        self.bedrock_agent = get_bedrock_agent()
        self.logger = logging.getLogger(__name__)
        self.parameters = {}
    
    async def execute(self) -> Dict[str, Any]:
        """Obtiene información del sistema Bedrock"""
        try:
            stats = await self.bedrock_agent.get_stats()
            
            return {
                "success": True,
                "stats": stats,
                "tool": self.name,
                "provider": "bedrock"
            }
            
        except Exception as e:
            self.logger.error(f"Error en BedrockKnowledgeGraphTool: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": self.name
            }


class BedrockModelTestTool:
    """Herramienta ADK para probar modelos de Bedrock"""
    
    def __init__(self):
        self.name = "bedrock_model_test"
        self.description = "Prueba la conectividad y funcionamiento de los modelos Bedrock"
        self.bedrock_agent = get_bedrock_agent()
        self.logger = logging.getLogger(__name__)
        self.parameters = {
            "test_type": {
                "type": "string",
                "description": "Tipo de prueba: llm, embedding, both",
                "enum": ["llm", "embedding", "both"],
                "default": "both"
            }
        }
    
    async def execute(self, test_type: str = "both") -> Dict[str, Any]:
        """Prueba los modelos de Bedrock"""
        try:
            results = {}
            
            if test_type in ["llm", "both"]:
                # Probar LLM
                try:
                    llm_response = await self.bedrock_agent.generate_response(
                        "¿Estás funcionando correctamente? Responde brevemente.",
                        "Eres un asistente de prueba."
                    )
                    results["llm_test"] = {
                        "success": True,
                        "response": llm_response[:100] + "..." if len(llm_response) > 100 else llm_response,
                        "model": self.bedrock_agent.llm_model
                    }
                except Exception as e:
                    results["llm_test"] = {
                        "success": False,
                        "error": str(e),
                        "model": self.bedrock_agent.llm_model
                    }
            
            if test_type in ["embedding", "both"]:
                # Probar embeddings
                try:
                    embedding = await self.bedrock_agent.generate_embedding("Texto de prueba")
                    results["embedding_test"] = {
                        "success": True,
                        "dimensions": len(embedding),
                        "model": self.bedrock_agent.embedding_model
                    }
                except Exception as e:
                    results["embedding_test"] = {
                        "success": False,
                        "error": str(e),
                        "model": self.bedrock_agent.embedding_model
                    }
            
            # Determinar éxito general
            all_success = all(
                result.get("success", False) 
                for result in results.values()
            )
            
            return {
                "success": all_success,
                "results": results,
                "tool": self.name,
                "provider": "bedrock"
            }
            
        except Exception as e:
            self.logger.error(f"Error en BedrockModelTestTool: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": self.name
            }


class BedrockADKAgent:
    """Agente ADK que integra todas las herramientas de Bedrock"""
    
    def __init__(self):
        self.bedrock_agent = get_bedrock_agent()
        self.logger = logging.getLogger(__name__)
        
        # Inicializar herramientas
        self.tools = {
            "query": BedrockLightRAGTool(),
            "load_documents": BedrockDocumentLoaderTool(),
            "system_info": BedrockKnowledgeGraphTool(),
            "model_test": BedrockModelTestTool()
        }
        
        self.logger.info("Agente ADK Bedrock inicializado")
    
    async def process_message(self, message: str) -> Dict[str, Any]:
        """Procesa mensaje usando herramientas de Bedrock"""
        try:
            message_lower = message.lower()
            
            # Determinar qué herramienta usar
            if any(word in message_lower for word in ["cargar", "load", "documentos"]):
                tool = self.tools["load_documents"]
                result = await tool.execute()
                
            elif any(word in message_lower for word in ["estadísticas", "stats", "info", "sistema"]):
                tool = self.tools["system_info"]
                result = await tool.execute()
                
            elif any(word in message_lower for word in ["probar", "test", "verificar", "check"]):
                tool = self.tools["model_test"]
                result = await tool.execute()
                
            else:
                # Usar herramienta de consulta por defecto
                tool = self.tools["query"]
                result = await tool.execute(message)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error procesando mensaje: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": message,
                "provider": "bedrock"
            }
    
    async def initialize_documents(self, docs_path: Optional[str] = None) -> bool:
        """Inicializa documentos en el agente"""
        try:
            path = Path(docs_path) if docs_path else None
            return await self.bedrock_agent.load_documents(path)
            
        except Exception as e:
            self.logger.error(f"Error inicializando documentos: {e}")
            return False
    
    async def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Obtiene información sobre herramientas disponibles"""
        tools_info = {}
        
        for name, tool in self.tools.items():
            tools_info[name] = {
                "name": tool.name,
                "description": tool.description,
                "parameters": getattr(tool, 'parameters', {})
            }
        
        return tools_info


# Función de conveniencia
def create_bedrock_adk_agent() -> BedrockADKAgent:
    """Crea una instancia del agente ADK Bedrock"""
    return BedrockADKAgent()
