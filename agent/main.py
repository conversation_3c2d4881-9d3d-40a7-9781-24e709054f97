"""
Punto de entrada principal del agente LightRAG
"""
import asyncio
import logging
import os
from pathlib import Path
from typing import Optional

from .lightrag_agent import get_agent
from .adk_integration import create_lightrag_adk_agent
from .config import DOCS_DIR, WORKING_DIR, LOGGING_CONFIG


async def initialize_agent(docs_path: Optional[str] = None) -> bool:
    """
    Inicializa el agente y carga los documentos
    
    Args:
        docs_path: Ruta opcional a los documentos
        
    Returns:
        bool: True si la inicialización fue exitosa
    """
    try:
        # Configurar logging
        os.makedirs(WORKING_DIR, exist_ok=True)
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG["level"]),
            format=LOGGING_CONFIG["format"],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG["file"]),
                logging.StreamHandler()
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("Inicializando agente LightRAG...")
        
        # Obtener instancia del agente
        agent = get_agent()
        
        # Cargar documentos
        path = Path(docs_path) if docs_path else DOCS_DIR
        success = await agent.load_documents(path)
        
        if success:
            logger.info("Agente inicializado exitosamente")
        else:
            logger.error("Error inicializando agente")
            
        return success
        
    except Exception as e:
        logging.error(f"Error en inicialización: {e}")
        return False


async def query_agent(question: str, mode: str = "hybrid") -> str:
    """
    Realiza una consulta al agente
    
    Args:
        question: Pregunta a realizar
        mode: Modo de búsqueda
        
    Returns:
        str: Respuesta del agente
    """
    try:
        agent = get_agent()
        response = await agent.query(question, mode)
        return response
        
    except Exception as e:
        logging.error(f"Error en consulta: {e}")
        return f"Error: {str(e)}"


async def run_interactive_mode():
    """
    Ejecuta el agente en modo interactivo
    """
    print("=== Agente LightRAG ===")
    print("Inicializando...")
    
    # Inicializar agente
    success = await initialize_agent()
    if not success:
        print("Error: No se pudo inicializar el agente")
        return
    
    print("Agente listo. Escribe 'quit' para salir.")
    print("Modos disponibles: naive, local, global, hybrid")
    print()
    
    while True:
        try:
            # Obtener pregunta del usuario
            question = input("Pregunta: ").strip()
            
            if question.lower() in ['quit', 'exit', 'salir']:
                break
                
            if not question:
                continue
            
            # Obtener modo (opcional)
            mode_input = input("Modo (hybrid): ").strip()
            mode = mode_input if mode_input else "hybrid"
            
            if mode not in ["naive", "local", "global", "hybrid"]:
                print("Modo inválido. Usando 'hybrid'")
                mode = "hybrid"
            
            # Procesar consulta
            print(f"\nProcesando consulta en modo '{mode}'...")
            response = await query_agent(question, mode)
            
            print(f"\nRespuesta:\n{response}\n")
            print("-" * 50)
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("¡Hasta luego!")


async def run_adk_mode():
    """
    Ejecuta el agente en modo ADK
    """
    print("=== Agente LightRAG con Google ADK ===")
    print("Inicializando...")
    
    try:
        # Crear agente ADK
        adk_agent = create_lightrag_adk_agent()
        
        # Inicializar documentos
        success = await adk_agent.initialize_documents()
        if not success:
            print("Error: No se pudieron cargar los documentos")
            return
        
        print("Agente ADK listo. Escribe 'quit' para salir.")
        print()
        
        while True:
            try:
                message = input("Mensaje: ").strip()
                
                if message.lower() in ['quit', 'exit', 'salir']:
                    break
                    
                if not message:
                    continue
                
                print("\nProcesando...")
                response = await adk_agent.process_message(message)
                
                print(f"\nRespuesta:\n{response}\n")
                print("-" * 50)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
        
        print("¡Hasta luego!")
        
    except Exception as e:
        print(f"Error inicializando agente ADK: {e}")


def main():
    """Función principal"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "adk":
        asyncio.run(run_adk_mode())
    else:
        asyncio.run(run_interactive_mode())


if __name__ == "__main__":
    main()
