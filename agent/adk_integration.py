"""
Integración del agente LightRAG con Google ADK
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from google.adk.core import Agent, AgentConfig
from google.adk.core.tools import Tool
from google.adk.core.llm import LLMConfig

from .lightrag_agent import get_agent


class LightRAGTool(Tool):
    """
    Herramienta que integra LightRAG con Google ADK
    """
    
    def __init__(self):
        super().__init__(
            name="lightrag_query",
            description="Consulta documentos usando LightRAG para obtener respuestas contextuales",
            parameters={
                "question": {
                    "type": "string",
                    "description": "Pregunta a realizar sobre los documentos",
                    "required": True
                },
                "mode": {
                    "type": "string", 
                    "description": "Modo de búsqueda: naive, local, global, hybrid",
                    "enum": ["naive", "local", "global", "hybrid"],
                    "default": "hybrid"
                }
            }
        )
        self.lightrag_agent = get_agent()
        self.logger = logging.getLogger(__name__)
    
    async def execute(self, question: str, mode: str = "hybrid") -> Dict[str, Any]:
        """
        Ejecuta una consulta usando LightRAG
        
        Args:
            question: Pregunta a realizar
            mode: Modo de búsqueda
            
        Returns:
            Dict con la respuesta y metadatos
        """
        try:
            self.logger.info(f"Ejecutando consulta LightRAG: {question}")
            
            response = await self.lightrag_agent.query(question, mode)
            
            return {
                "success": True,
                "response": response,
                "mode": mode,
                "question": question
            }
            
        except Exception as e:
            self.logger.error(f"Error en LightRAGTool: {e}")
            return {
                "success": False,
                "error": str(e),
                "question": question
            }


class DocumentLoaderTool(Tool):
    """
    Herramienta para cargar documentos en LightRAG
    """
    
    def __init__(self):
        super().__init__(
            name="load_documents",
            description="Carga documentos en la base de conocimiento de LightRAG",
            parameters={
                "docs_path": {
                    "type": "string",
                    "description": "Ruta a los documentos a cargar (opcional)",
                    "required": False
                }
            }
        )
        self.lightrag_agent = get_agent()
        self.logger = logging.getLogger(__name__)
    
    async def execute(self, docs_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Carga documentos en LightRAG
        
        Args:
            docs_path: Ruta opcional a los documentos
            
        Returns:
            Dict con el resultado de la carga
        """
        try:
            self.logger.info(f"Cargando documentos desde: {docs_path or 'directorio por defecto'}")
            
            path = Path(docs_path) if docs_path else None
            success = await self.lightrag_agent.load_documents(path)
            
            return {
                "success": success,
                "message": "Documentos cargados exitosamente" if success else "Error cargando documentos",
                "docs_path": docs_path or "directorio por defecto"
            }
            
        except Exception as e:
            self.logger.error(f"Error en DocumentLoaderTool: {e}")
            return {
                "success": False,
                "error": str(e)
            }


class KnowledgeGraphTool(Tool):
    """
    Herramienta para obtener información del grafo de conocimiento
    """
    
    def __init__(self):
        super().__init__(
            name="knowledge_graph_info",
            description="Obtiene información sobre el grafo de conocimiento de LightRAG",
            parameters={}
        )
        self.lightrag_agent = get_agent()
        self.logger = logging.getLogger(__name__)
    
    async def execute(self) -> Dict[str, Any]:
        """
        Obtiene información del grafo de conocimiento
        
        Returns:
            Dict con información del grafo
        """
        try:
            info = await self.lightrag_agent.get_knowledge_graph_info()
            return info
            
        except Exception as e:
            self.logger.error(f"Error en KnowledgeGraphTool: {e}")
            return {
                "success": False,
                "error": str(e)
            }


class LightRAGADKAgent:
    """
    Agente que integra LightRAG con Google ADK
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.agent = None
        self._setup_agent()
    
    def _setup_agent(self):
        """Configura el agente ADK con las herramientas de LightRAG"""
        try:
            # Configuración del agente
            agent_config = AgentConfig(
                name="LightRAG Agent",
                description="Agente que utiliza LightRAG para consultas sobre documentos",
                llm_config=LLMConfig(
                    model="gpt-4o-mini",
                    temperature=0.1,
                    max_tokens=4000
                )
            )
            
            # Crear herramientas
            tools = [
                LightRAGTool(),
                DocumentLoaderTool(),
                KnowledgeGraphTool()
            ]
            
            # Crear agente
            self.agent = Agent(
                config=agent_config,
                tools=tools
            )
            
            self.logger.info("Agente ADK configurado exitosamente")
            
        except Exception as e:
            self.logger.error(f"Error configurando agente ADK: {e}")
            raise
    
    async def process_message(self, message: str) -> str:
        """
        Procesa un mensaje usando el agente ADK
        
        Args:
            message: Mensaje a procesar
            
        Returns:
            str: Respuesta del agente
        """
        try:
            if not self.agent:
                raise ValueError("Agente no configurado")
            
            response = await self.agent.process(message)
            return response
            
        except Exception as e:
            self.logger.error(f"Error procesando mensaje: {e}")
            return f"Error: {str(e)}"
    
    async def initialize_documents(self, docs_path: Optional[str] = None) -> bool:
        """
        Inicializa los documentos en LightRAG
        
        Args:
            docs_path: Ruta opcional a los documentos
            
        Returns:
            bool: True si la inicialización fue exitosa
        """
        try:
            lightrag_agent = get_agent()
            path = Path(docs_path) if docs_path else None
            return await lightrag_agent.load_documents(path)
            
        except Exception as e:
            self.logger.error(f"Error inicializando documentos: {e}")
            return False


# Función de conveniencia para crear el agente
def create_lightrag_adk_agent(config: Optional[Dict[str, Any]] = None) -> LightRAGADKAgent:
    """
    Crea una instancia del agente LightRAG-ADK
    
    Args:
        config: Configuración opcional del agente
        
    Returns:
        LightRAGADKAgent: Instancia del agente
    """
    return LightRAGADKAgent(config)
