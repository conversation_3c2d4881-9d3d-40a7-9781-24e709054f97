"""
Agente principal que utiliza LightRAG para procesamiento de documentos
"""
import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import asyncio

from lightrag import LightRAG, QueryParam
from lightrag.llm import gpt_4o_mini_complete, gpt_4o_complete
from lightrag.utils import EmbeddingFunc

from .config import (
    LLM_CONFIG,
    EMBEDDING_CONFIG,
    LIGHTRAG_CONFIG,
    DOCS_DIR,
    WORKING_DIR,
    LOGGING_CONFIG,
    AVAILABLE_MODELS,
    LOCAL_MODEL_CONFIG,
)


class LightRAGAgent:
    """
    Agente principal que utiliza LightRAG para procesamiento de documentos
    y generación de respuestas contextuales.
    """

    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        self.rag = None
        self.llm_provider = LLM_CONFIG["provider"]
        self.embedding_provider = EMBEDDING_CONFIG["provider"]
        self._initialize_models()
        self._initialize_lightrag()

    def _initialize_models(self):
        """Inicializa los modelos según el proveedor disponible"""
        self.logger.info(f"Inicializando con LLM: {self.llm_provider}, Embeddings: {self.embedding_provider}")

        # Inicializar cliente Bedrock si es necesario
        if self.llm_provider == "bedrock" or self.embedding_provider == "bedrock":
            try:
                import boto3

                # Crear sesión con perfil IA
                self.bedrock_session = boto3.Session(profile_name=LLM_CONFIG.get("profile", "IA"))
                self.bedrock_client = self.bedrock_session.client(
                    'bedrock-runtime',
                    region_name=LLM_CONFIG.get("region", "us-east-2")
                )
                self.logger.info("Cliente Bedrock inicializado correctamente")

            except Exception as e:
                self.logger.error(f"Error inicializando cliente Bedrock: {e}")
                raise

        # Inicializar modelos de embeddings locales si es necesario
        if self.embedding_provider == "sentence_transformers":
            try:
                from sentence_transformers import SentenceTransformer
                cache_dir = LOCAL_MODEL_CONFIG["sentence_transformers"]["cache_dir"]
                os.makedirs(cache_dir, exist_ok=True)

                self.embedding_model = SentenceTransformer(
                    EMBEDDING_CONFIG["model"],
                    cache_folder=cache_dir
                )
                self.logger.info(f"Modelo de embeddings cargado: {EMBEDDING_CONFIG['model']}")
            except Exception as e:
                self.logger.error(f"Error cargando modelo de embeddings: {e}")
                raise

        # Inicializar modelo LLM local si es necesario
        if self.llm_provider == "transformers":
            try:
                from transformers import AutoTokenizer, AutoModelForCausalLM
                import torch

                model_name = LLM_CONFIG["model"]
                cache_dir = LOCAL_MODEL_CONFIG["transformers"]["cache_dir"]
                os.makedirs(cache_dir, exist_ok=True)

                self.logger.info(f"Cargando tokenizer: {model_name}")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    model_name,
                    cache_dir=cache_dir,
                    padding_side="left"
                )

                # Configurar pad_token si no existe
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token

                self.logger.info(f"Cargando modelo: {model_name}")
                self.llm_model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    cache_dir=cache_dir,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                    low_cpu_mem_usage=True
                )

                # Mover a GPU si está disponible
                if torch.cuda.is_available():
                    self.llm_model = self.llm_model.cuda()
                    self.logger.info("Modelo movido a GPU")

                self.logger.info(f"Modelo LLM local cargado: {model_name}")
            except Exception as e:
                self.logger.error(f"Error cargando modelo LLM local: {e}")
                raise
    
    def setup_logging(self):
        """Configura el sistema de logging"""
        os.makedirs(WORKING_DIR, exist_ok=True)
        
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG["level"]),
            format=LOGGING_CONFIG["format"],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG["file"]),
                logging.StreamHandler()
            ]
        )
    
    def _initialize_lightrag(self):
        """Inicializa LightRAG con la configuración especificada"""
        try:
            # Crear directorio de trabajo si no existe
            os.makedirs(WORKING_DIR, exist_ok=True)

            # Configurar función LLM según el proveedor
            llm_model_func = self._get_llm_function()

            # Configurar función de embeddings según el proveedor
            embedding_func = self._get_embedding_function()

            # Inicializar LightRAG
            self.rag = LightRAG(
                working_dir=LIGHTRAG_CONFIG["working_dir"],
                llm_model_func=llm_model_func,
                embedding_func=EmbeddingFunc(
                    embedding_dim=EMBEDDING_CONFIG["dimensions"],
                    max_token_size=8192,
                    func=embedding_func
                ),
                **{k: v for k, v in LIGHTRAG_CONFIG.items() if k != "working_dir"}
            )

            self.logger.info("LightRAG inicializado correctamente")

        except Exception as e:
            self.logger.error(f"Error inicializando LightRAG: {e}")
            raise

    def _get_llm_function(self):
        """Retorna la función LLM apropiada según el proveedor"""
        if self.llm_provider == "bedrock":
            async def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs) -> str:
                try:
                    import json

                    # Construir el prompt para Claude
                    full_prompt = ""
                    if system_prompt:
                        full_prompt += f"System: {system_prompt}\n\n"

                    # Agregar historial de mensajes
                    for msg in history_messages[-3:]:  # Solo últimos 3 mensajes
                        role = msg.get("role", "user")
                        content = msg.get("content", "")
                        if role == "user":
                            full_prompt += f"Human: {content}\n"
                        elif role == "assistant":
                            full_prompt += f"Assistant: {content}\n"

                    full_prompt += f"Human: {prompt}\nAssistant:"

                    # Preparar el cuerpo de la solicitud para Claude
                    body = {
                        "prompt": full_prompt,
                        "max_tokens_to_sample": LLM_CONFIG.get("max_tokens", 4000),
                        "temperature": LLM_CONFIG.get("temperature", 0.1),
                        "top_p": LLM_CONFIG.get("top_p", 0.9),
                        "stop_sequences": ["\n\nHuman:"]
                    }

                    # Llamar a Bedrock
                    response = self.bedrock_client.invoke_model(
                        modelId=LLM_CONFIG["model"],
                        body=json.dumps(body),
                        contentType="application/json",
                        accept="application/json"
                    )

                    # Procesar respuesta
                    response_body = json.loads(response['body'].read())
                    completion = response_body.get('completion', '')

                    # Limpiar respuesta
                    completion = completion.strip()
                    if len(completion) > 2000:
                        completion = completion[:2000] + "..."

                    return completion if completion else "Lo siento, no pude generar una respuesta."

                except Exception as e:
                    self.logger.error(f"Error con Bedrock: {e}")
                    return f"Error procesando consulta con Bedrock: {str(e)}"

            return llm_model_func

        elif self.llm_provider == "openai":
            async def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs) -> str:
                return await gpt_4o_mini_complete(
                    prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    api_key=LLM_CONFIG["api_key"],
                    **kwargs
                )
            return llm_model_func

        elif self.llm_provider == "ollama":
            async def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs) -> str:
                try:
                    import ollama

                    # Construir el prompt completo
                    full_prompt = ""
                    if system_prompt:
                        full_prompt += f"System: {system_prompt}\n\n"

                    # Agregar historial de mensajes
                    for msg in history_messages:
                        role = msg.get("role", "user")
                        content = msg.get("content", "")
                        full_prompt += f"{role.capitalize()}: {content}\n"

                    full_prompt += f"User: {prompt}\nAssistant:"

                    response = ollama.generate(
                        model=LLM_CONFIG["model"],
                        prompt=full_prompt,
                        options={
                            "temperature": LLM_CONFIG.get("temperature", 0.1),
                            "num_predict": LLM_CONFIG.get("max_tokens", 1000)
                        }
                    )

                    return response["response"]

                except Exception as e:
                    self.logger.error(f"Error con Ollama: {e}")
                    return f"Error: {str(e)}"

            return llm_model_func

        elif self.llm_provider == "transformers":
            async def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs) -> str:
                try:
                    import torch

                    # Construir el prompt para modelos conversacionales
                    if "DialoGPT" in LLM_CONFIG["model"]:
                        # Para DialoGPT, usar formato conversacional
                        conversation = ""
                        if system_prompt:
                            conversation += f"System: {system_prompt}\n"

                        # Agregar historial
                        for msg in history_messages[-3:]:  # Solo últimos 3 mensajes
                            role = msg.get("role", "user")
                            content = msg.get("content", "")
                            if role == "user":
                                conversation += f"User: {content}\n"
                            elif role == "assistant":
                                conversation += f"Bot: {content}\n"

                        conversation += f"User: {prompt}\nBot:"
                        full_prompt = conversation
                    else:
                        # Para otros modelos, formato simple
                        full_prompt = ""
                        if system_prompt:
                            full_prompt += f"{system_prompt}\n\n"
                        full_prompt += f"Question: {prompt}\nAnswer:"

                    # Tokenizar con límite de longitud
                    inputs = self.tokenizer.encode(
                        full_prompt,
                        return_tensors="pt",
                        max_length=512,  # Limitar entrada
                        truncation=True
                    )

                    # Mover a GPU si está disponible
                    if torch.cuda.is_available():
                        inputs = inputs.cuda()

                    # Generar respuesta
                    with torch.no_grad():
                        outputs = self.llm_model.generate(
                            inputs,
                            max_new_tokens=LLM_CONFIG.get("max_tokens", 200),
                            temperature=LLM_CONFIG.get("temperature", 0.7),
                            do_sample=True,
                            pad_token_id=self.tokenizer.pad_token_id,
                            eos_token_id=self.tokenizer.eos_token_id,
                            repetition_penalty=1.1,
                            no_repeat_ngram_size=3
                        )

                    # Decodificar solo la nueva parte generada
                    new_tokens = outputs[0][inputs.shape[1]:]
                    response = self.tokenizer.decode(new_tokens, skip_special_tokens=True)

                    # Limpiar respuesta
                    response = response.strip()
                    if response.startswith("Bot:"):
                        response = response[4:].strip()

                    # Limitar longitud de respuesta
                    if len(response) > 500:
                        response = response[:500] + "..."

                    return response if response else "Lo siento, no pude generar una respuesta."

                except Exception as e:
                    self.logger.error(f"Error con transformers: {e}")
                    return f"Error procesando consulta: {str(e)}"

            return llm_model_func

        else:
            raise ValueError(f"Proveedor LLM no soportado: {self.llm_provider}")

    def _get_embedding_function(self):
        """Retorna la función de embeddings apropiada según el proveedor"""
        if self.embedding_provider == "bedrock":
            async def embedding_func(texts: List[str]) -> List[List[float]]:
                try:
                    import json

                    embeddings = []
                    for text in texts:
                        # Preparar el cuerpo de la solicitud para Titan Embeddings
                        body = {
                            "inputText": text
                        }

                        # Llamar a Bedrock
                        response = self.bedrock_client.invoke_model(
                            modelId=EMBEDDING_CONFIG["model"],
                            body=json.dumps(body),
                            contentType="application/json",
                            accept="application/json"
                        )

                        # Procesar respuesta
                        response_body = json.loads(response['body'].read())
                        embedding = response_body.get('embedding', [])

                        # Normalizar si está configurado
                        if EMBEDDING_CONFIG.get("normalize", True) and embedding:
                            import numpy as np
                            embedding = np.array(embedding)
                            embedding = embedding / np.linalg.norm(embedding)
                            embedding = embedding.tolist()

                        embeddings.append(embedding)

                    return embeddings

                except Exception as e:
                    self.logger.error(f"Error con Bedrock embeddings: {e}")
                    # Retornar embeddings dummy en caso de error
                    return [[0.0] * EMBEDDING_CONFIG["dimensions"] for _ in texts]

            return embedding_func

        elif self.embedding_provider == "openai":
            async def embedding_func(texts: List[str]) -> List[List[float]]:
                import openai
                client = openai.AsyncOpenAI(api_key=EMBEDDING_CONFIG["api_key"])

                embeddings = []
                for text in texts:
                    response = await client.embeddings.create(
                        model=EMBEDDING_CONFIG["model"],
                        input=text
                    )
                    embeddings.append(response.data[0].embedding)

                return embeddings

            return embedding_func

        elif self.embedding_provider == "sentence_transformers":
            async def embedding_func(texts: List[str]) -> List[List[float]]:
                try:
                    # sentence-transformers es síncrono, pero lo envolvemos en async
                    embeddings = self.embedding_model.encode(texts, convert_to_tensor=False)
                    return embeddings.tolist() if hasattr(embeddings, 'tolist') else embeddings

                except Exception as e:
                    self.logger.error(f"Error con sentence-transformers: {e}")
                    # Retornar embeddings dummy en caso de error
                    return [[0.0] * EMBEDDING_CONFIG["dimensions"] for _ in texts]

            return embedding_func

        else:
            raise ValueError(f"Proveedor de embeddings no soportado: {self.embedding_provider}")
    
    async def load_documents(self, docs_path: Optional[Path] = None) -> bool:
        """
        Carga documentos desde el directorio especificado
        
        Args:
            docs_path: Ruta a los documentos (por defecto usa DOCS_DIR)
            
        Returns:
            bool: True si la carga fue exitosa
        """
        if docs_path is None:
            docs_path = DOCS_DIR
            
        try:
            self.logger.info(f"Cargando documentos desde: {docs_path}")
            
            # Buscar todos los archivos de texto en el directorio
            supported_extensions = {'.md', '.txt', '.json'}
            documents = []
            
            for file_path in docs_path.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            documents.append({
                                'path': str(file_path),
                                'content': content
                            })
                    except Exception as e:
                        self.logger.warning(f"Error leyendo {file_path}: {e}")
            
            # Insertar documentos en LightRAG
            for doc in documents:
                await self.rag.ainsert(doc['content'])
                self.logger.info(f"Documento cargado: {doc['path']}")
            
            self.logger.info(f"Se cargaron {len(documents)} documentos exitosamente")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cargando documentos: {e}")
            return False
    
    async def query(self, question: str, mode: str = "hybrid") -> str:
        """
        Realiza una consulta al sistema RAG
        
        Args:
            question: Pregunta a realizar
            mode: Modo de búsqueda ("naive", "local", "global", "hybrid")
            
        Returns:
            str: Respuesta generada
        """
        try:
            self.logger.info(f"Procesando consulta: {question}")
            
            response = await self.rag.aquery(
                question,
                param=QueryParam(mode=mode)
            )
            
            self.logger.info("Consulta procesada exitosamente")
            return response
            
        except Exception as e:
            self.logger.error(f"Error procesando consulta: {e}")
            return f"Error: {str(e)}"
    
    async def get_knowledge_graph_info(self) -> Dict[str, Any]:
        """
        Obtiene información sobre el grafo de conocimiento
        
        Returns:
            Dict con estadísticas del grafo
        """
        try:
            # Aquí podríamos implementar estadísticas del grafo
            # Por ahora retornamos información básica
            return {
                "status": "active",
                "working_dir": LIGHTRAG_CONFIG["working_dir"],
                "message": "Grafo de conocimiento activo"
            }
        except Exception as e:
            self.logger.error(f"Error obteniendo info del grafo: {e}")
            return {"status": "error", "message": str(e)}


# Instancia global del agente
agent_instance = None

def get_agent() -> LightRAGAgent:
    """Obtiene la instancia global del agente"""
    global agent_instance
    if agent_instance is None:
        agent_instance = LightRAGAgent()
    return agent_instance
