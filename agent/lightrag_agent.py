"""
Agente principal que utiliza LightRAG para procesamiento de documentos
"""
import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import asyncio

from lightrag import LightRAG, QueryParam
from lightrag.llm import gpt_4o_mini_complete, gpt_4o_complete
from lightrag.utils import EmbeddingFunc

from .config import (
    LLM_CONFIG,
    EMBEDDING_CONFIG,
    LIGHTRAG_CONFIG,
    DOCS_DIR,
    WORKING_DIR,
    LOGGING_CONFIG,
)


class LightRAGAgent:
    """
    Agente principal que utiliza LightRAG para procesamiento de documentos
    y generación de respuestas contextuales.
    """
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        self.rag = None
        self._initialize_lightrag()
    
    def setup_logging(self):
        """Configura el sistema de logging"""
        os.makedirs(WORKING_DIR, exist_ok=True)
        
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG["level"]),
            format=LOGGING_CONFIG["format"],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG["file"]),
                logging.StreamHandler()
            ]
        )
    
    def _initialize_lightrag(self):
        """Inicializa LightRAG con la configuración especificada"""
        try:
            # Crear directorio de trabajo si no existe
            os.makedirs(WORKING_DIR, exist_ok=True)
            
            # Configurar función LLM
            async def llm_model_func(
                prompt, system_prompt=None, history_messages=[], **kwargs
            ) -> str:
                return await gpt_4o_mini_complete(
                    prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    api_key=LLM_CONFIG["api_key"],
                    **kwargs
                )
            
            # Configurar función de embeddings
            async def embedding_func(texts: List[str]) -> List[List[float]]:
                # Aquí usaremos OpenAI embeddings
                import openai
                client = openai.AsyncOpenAI(api_key=EMBEDDING_CONFIG["api_key"])
                
                embeddings = []
                for text in texts:
                    response = await client.embeddings.create(
                        model=EMBEDDING_CONFIG["model"],
                        input=text
                    )
                    embeddings.append(response.data[0].embedding)
                
                return embeddings
            
            # Inicializar LightRAG
            self.rag = LightRAG(
                working_dir=LIGHTRAG_CONFIG["working_dir"],
                llm_model_func=llm_model_func,
                embedding_func=EmbeddingFunc(
                    embedding_dim=EMBEDDING_CONFIG["dimensions"],
                    max_token_size=8192,
                    func=embedding_func
                ),
                **{k: v for k, v in LIGHTRAG_CONFIG.items() if k != "working_dir"}
            )
            
            self.logger.info("LightRAG inicializado correctamente")
            
        except Exception as e:
            self.logger.error(f"Error inicializando LightRAG: {e}")
            raise
    
    async def load_documents(self, docs_path: Optional[Path] = None) -> bool:
        """
        Carga documentos desde el directorio especificado
        
        Args:
            docs_path: Ruta a los documentos (por defecto usa DOCS_DIR)
            
        Returns:
            bool: True si la carga fue exitosa
        """
        if docs_path is None:
            docs_path = DOCS_DIR
            
        try:
            self.logger.info(f"Cargando documentos desde: {docs_path}")
            
            # Buscar todos los archivos de texto en el directorio
            supported_extensions = {'.md', '.txt', '.json'}
            documents = []
            
            for file_path in docs_path.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            documents.append({
                                'path': str(file_path),
                                'content': content
                            })
                    except Exception as e:
                        self.logger.warning(f"Error leyendo {file_path}: {e}")
            
            # Insertar documentos en LightRAG
            for doc in documents:
                await self.rag.ainsert(doc['content'])
                self.logger.info(f"Documento cargado: {doc['path']}")
            
            self.logger.info(f"Se cargaron {len(documents)} documentos exitosamente")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cargando documentos: {e}")
            return False
    
    async def query(self, question: str, mode: str = "hybrid") -> str:
        """
        Realiza una consulta al sistema RAG
        
        Args:
            question: Pregunta a realizar
            mode: Modo de búsqueda ("naive", "local", "global", "hybrid")
            
        Returns:
            str: Respuesta generada
        """
        try:
            self.logger.info(f"Procesando consulta: {question}")
            
            response = await self.rag.aquery(
                question,
                param=QueryParam(mode=mode)
            )
            
            self.logger.info("Consulta procesada exitosamente")
            return response
            
        except Exception as e:
            self.logger.error(f"Error procesando consulta: {e}")
            return f"Error: {str(e)}"
    
    async def get_knowledge_graph_info(self) -> Dict[str, Any]:
        """
        Obtiene información sobre el grafo de conocimiento
        
        Returns:
            Dict con estadísticas del grafo
        """
        try:
            # Aquí podríamos implementar estadísticas del grafo
            # Por ahora retornamos información básica
            return {
                "status": "active",
                "working_dir": LIGHTRAG_CONFIG["working_dir"],
                "message": "Grafo de conocimiento activo"
            }
        except Exception as e:
            self.logger.error(f"Error obteniendo info del grafo: {e}")
            return {"status": "error", "message": str(e)}


# Instancia global del agente
agent_instance = None

def get_agent() -> LightRAGAgent:
    """Obtiene la instancia global del agente"""
    global agent_instance
    if agent_instance is None:
        agent_instance = LightRAGAgent()
    return agent_instance
