"""
Configuración para LightRAG
"""
import os
from pathlib import Path

# Configuración de directorios
PROJECT_ROOT = Path(__file__).parent.parent.parent
DOCS_DIR = PROJECT_ROOT / "docs"
WORKING_DIR = PROJECT_ROOT / "agent" / "lightrag" / "working_dir"

# Detectar modelos disponibles
def detect_available_models():
    """Detecta qué modelos están disponibles en el sistema"""
    available = {
        "openai": bool(os.getenv("OPENAI_API_KEY")),
        "ollama": False,
        "sentence_transformers": True,  # Siempre disponible después de la instalación
    }

    # Verificar si Ollama está disponible
    try:
        import ollama
        # Intentar conectar a Ollama
        ollama.list()
        available["ollama"] = True
    except:
        available["ollama"] = False

    return available

AVAILABLE_MODELS = detect_available_models()

# Configuración de LLM (priorizando Hugging Face transformers)
# Orden de prioridad: transformers -> ollama -> openai
if True:  # Siempre usar transformers como primario
    LLM_CONFIG = {
        "provider": "transformers",
        "model": "microsoft/DialoGPT-medium",  # Modelo conversacional
        "temperature": 0.1,
        "max_tokens": 1000,
        "device": "auto",  # Usar GPU si está disponible
        "torch_dtype": "auto",
    }
elif AVAILABLE_MODELS["ollama"]:
    LLM_CONFIG = {
        "provider": "ollama",
        "model": "llama3.2:3b",
        "base_url": "http://localhost:11434",
        "temperature": 0.1,
        "max_tokens": 4000,
    }
elif AVAILABLE_MODELS["openai"]:
    LLM_CONFIG = {
        "provider": "openai",
        "model": "gpt-4o-mini",
        "api_key": os.getenv("OPENAI_API_KEY"),
        "temperature": 0.1,
        "max_tokens": 4000,
    }

# Configuración de embeddings (con fallback a sentence-transformers)
if AVAILABLE_MODELS["openai"]:
    EMBEDDING_CONFIG = {
        "provider": "openai",
        "model": "text-embedding-3-small",
        "api_key": os.getenv("OPENAI_API_KEY"),
        "dimensions": 1536,
    }
else:
    EMBEDDING_CONFIG = {
        "provider": "sentence_transformers",
        "model": "all-MiniLM-L6-v2",  # Modelo ligero y eficiente
        "dimensions": 384,
    }

# Configuración de base de datos vectorial (ChromaDB)
VECTOR_DB_CONFIG = {
    "persist_directory": str(WORKING_DIR / "chroma_db"),
    "collection_name": "agent_docs",
}

# Configuración de base de datos de grafos (Neo4j)
GRAPH_DB_CONFIG = {
    "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
    "username": os.getenv("NEO4J_USERNAME", "neo4j"),
    "password": os.getenv("NEO4J_PASSWORD", "password"),
    "database": os.getenv("NEO4J_DATABASE", "neo4j"),
}

# Configuración de LightRAG (adaptada según el modelo disponible)
LIGHTRAG_CONFIG = {
    "working_dir": str(WORKING_DIR),
    "chunk_token_size": 800 if LLM_CONFIG["provider"] != "openai" else 1200,  # Más conservador para modelos locales
    "chunk_overlap_token_size": 50 if LLM_CONFIG["provider"] != "openai" else 100,
    "tiktoken_model_name": LLM_CONFIG.get("model", "gpt-4o-mini"),
    "entity_extract_max_gleaning": 1,
    "entity_summary_to_max_tokens": 300 if LLM_CONFIG["provider"] != "openai" else 500,
    "node_description_language": "ES",  # Español
}

# Configuración específica para modelos locales
LOCAL_MODEL_CONFIG = {
    "transformers": {
        "available_models": [
            "microsoft/DialoGPT-medium",
            "microsoft/DialoGPT-small",
            "distilgpt2",
            "gpt2",
            "facebook/blenderbot-400M-distill"
        ],
        "recommended": "microsoft/DialoGPT-medium",
        "cache_dir": str(WORKING_DIR / "transformers_cache"),
        "device_map": "auto",
        "torch_dtype": "auto"
    },
    "ollama": {
        "available_models": ["llama3.2:3b", "llama3.2:1b", "mistral:7b", "phi3:mini"],
        "recommended": "llama3.2:3b",
        "install_command": "ollama pull llama3.2:3b"
    },
    "sentence_transformers": {
        "available_models": ["all-MiniLM-L6-v2", "all-mpnet-base-v2", "paraphrase-MiniLM-L6-v2"],
        "recommended": "all-MiniLM-L6-v2",
        "cache_dir": str(WORKING_DIR / "sentence_transformers_cache")
    }
}

# Configuración de AWS SageMaker
SAGEMAKER_CONFIG = {
    "profile": "IA",
    "region": "us-east-1",
    "instance_type": "ml.t3.medium",
    "role_arn": os.getenv("SAGEMAKER_ROLE_ARN"),
}

# Configuración de logging
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": str(WORKING_DIR / "agent.log"),
}
