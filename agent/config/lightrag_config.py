"""
Configuración para LightRAG
"""
import os
from pathlib import Path

# Configuración de directorios
PROJECT_ROOT = Path(__file__).parent.parent.parent
DOCS_DIR = PROJECT_ROOT / "docs"
WORKING_DIR = PROJECT_ROOT / "agent" / "lightrag" / "working_dir"

# Configuración de LLM
LLM_CONFIG = {
    "model": "gpt-4o-mini",  # Modelo por defecto
    "api_key": os.getenv("OPENAI_API_KEY"),
    "temperature": 0.1,
    "max_tokens": 4000,
}

# Configuración de embeddings
EMBEDDING_CONFIG = {
    "model": "text-embedding-3-small",
    "api_key": os.getenv("OPENAI_API_KEY"),
    "dimensions": 1536,
}

# Configuración de base de datos vectorial (ChromaDB)
VECTOR_DB_CONFIG = {
    "persist_directory": str(WORKING_DIR / "chroma_db"),
    "collection_name": "agent_docs",
}

# Configuración de base de datos de grafos (Neo4j)
GRAPH_DB_CONFIG = {
    "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
    "username": os.getenv("NEO4J_USERNAME", "neo4j"),
    "password": os.getenv("NEO4J_PASSWORD", "password"),
    "database": os.getenv("NEO4J_DATABASE", "neo4j"),
}

# Configuración de LightRAG
LIGHTRAG_CONFIG = {
    "working_dir": str(WORKING_DIR),
    "chunk_token_size": 1200,
    "chunk_overlap_token_size": 100,
    "tiktoken_model_name": "gpt-4o-mini",
    "entity_extract_max_gleaning": 1,
    "entity_summary_to_max_tokens": 500,
    "node_description_language": "ES",  # Español
}

# Configuración de AWS SageMaker
SAGEMAKER_CONFIG = {
    "profile": "IA",
    "region": "us-east-1",
    "instance_type": "ml.t3.medium",
    "role_arn": os.getenv("SAGEMAKER_ROLE_ARN"),
}

# Configuración de logging
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": str(WORKING_DIR / "agent.log"),
}
