"""
Agente RAG usando AWS Bedrock para LLM y embeddings
"""
import os
import logging
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

import boto3
from botocore.exceptions import NoCredentialsError, ClientError

from .config import (
    LLM_CONFIG,
    EMBEDDING_CONFIG,
    LIGHTRAG_CONFIG,
    DOCS_DIR,
    WORKING_DIR,
    LOGGING_CONFIG,
    LOCAL_MODEL_CONFIG,
)


class BedrockRAGAgent:
    """Agente RAG usando AWS Bedrock"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Configuración de Bedrock
        self.profile = LLM_CONFIG.get("profile", "IA")
        self.region = LLM_CONFIG.get("region", "us-east-2")
        self.llm_model = LLM_CONFIG.get("model", "anthropic.claude-3-haiku-20240307-v1:0")
        self.embedding_model = EMBEDDING_CONFIG.get("model", "amazon.titan-embed-text-v2:0")
        
        # Documentos y embeddings
        self.documents = []
        self.doc_embeddings = []
        
        # Inicializar cliente Bedrock
        self._initialize_bedrock()
    
    def setup_logging(self):
        """Configura logging"""
        os.makedirs(WORKING_DIR, exist_ok=True)
        
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG["level"]),
            format=LOGGING_CONFIG["format"],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG["file"]),
                logging.StreamHandler()
            ]
        )
    
    def _initialize_bedrock(self):
        """Inicializa el cliente de Bedrock"""
        try:
            # Crear sesión con perfil específico
            self.session = boto3.Session(profile_name=self.profile)
            self.bedrock_client = self.session.client(
                'bedrock-runtime', 
                region_name=self.region
            )
            
            self.logger.info(f"Cliente Bedrock inicializado - Perfil: {self.profile}, Región: {self.region}")
            
        except NoCredentialsError:
            self.logger.error("No se encontraron credenciales AWS")
            raise
        except Exception as e:
            self.logger.error(f"Error inicializando Bedrock: {e}")
            raise
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Genera embedding para un texto usando Bedrock"""
        try:
            body = {
                "inputText": text
            }
            
            response = self.bedrock_client.invoke_model(
                modelId=self.embedding_model,
                body=json.dumps(body),
                contentType="application/json",
                accept="application/json"
            )
            
            response_body = json.loads(response['body'].read())
            embedding = response_body.get('embedding', [])
            
            # Normalizar si está configurado
            if EMBEDDING_CONFIG.get("normalize", True) and embedding:
                import numpy as np
                embedding = np.array(embedding)
                embedding = embedding / np.linalg.norm(embedding)
                embedding = embedding.tolist()
            
            return embedding
            
        except Exception as e:
            self.logger.error(f"Error generando embedding: {e}")
            return [0.0] * EMBEDDING_CONFIG.get("dimensions", 1536)
    
    async def generate_response(self, prompt: str, system_prompt: str = None) -> str:
        """Genera respuesta usando el modelo LLM de Bedrock"""
        try:
            # Construir prompt completo
            full_prompt = ""
            if system_prompt:
                full_prompt += f"System: {system_prompt}\n\n"
            
            full_prompt += f"Human: {prompt}\nAssistant:"
            
            # Configurar parámetros del modelo
            body = {
                "prompt": full_prompt,
                "max_tokens_to_sample": LLM_CONFIG.get("max_tokens", 4000),
                "temperature": LLM_CONFIG.get("temperature", 0.1),
                "top_p": LLM_CONFIG.get("top_p", 0.9),
                "stop_sequences": ["\n\nHuman:"]
            }
            
            # Llamar a Bedrock
            response = self.bedrock_client.invoke_model(
                modelId=self.llm_model,
                body=json.dumps(body),
                contentType="application/json",
                accept="application/json"
            )
            
            # Procesar respuesta
            response_body = json.loads(response['body'].read())
            completion = response_body.get('completion', '')
            
            # Limpiar respuesta
            completion = completion.strip()
            if len(completion) > 2000:
                completion = completion[:2000] + "..."
            
            return completion if completion else "Lo siento, no pude generar una respuesta."
            
        except Exception as e:
            self.logger.error(f"Error generando respuesta: {e}")
            return f"Error: {str(e)}"
    
    async def load_documents(self, docs_path: Optional[Path] = None) -> bool:
        """Carga documentos y genera embeddings"""
        try:
            if docs_path is None:
                docs_path = DOCS_DIR
            
            self.logger.info(f"Cargando documentos desde: {docs_path}")
            
            # Buscar archivos
            supported_extensions = {'.md', '.txt', '.json'}
            self.documents = []
            
            for file_path in docs_path.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            self.documents.append({
                                'text': content,
                                'source': str(file_path)
                            })
                    except Exception as e:
                        self.logger.warning(f"Error leyendo {file_path}: {e}")
            
            # Generar embeddings para todos los documentos
            self.logger.info(f"Generando embeddings para {len(self.documents)} documentos...")
            self.doc_embeddings = []
            
            for i, doc in enumerate(self.documents):
                embedding = await self.generate_embedding(doc['text'])
                self.doc_embeddings.append(embedding)
                
                if (i + 1) % 5 == 0:  # Log cada 5 documentos
                    self.logger.info(f"Procesados {i + 1}/{len(self.documents)} documentos")
            
            self.logger.info(f"Cargados {len(self.documents)} documentos con embeddings")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cargando documentos: {e}")
            return False
    
    async def search_documents(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Busca documentos relevantes usando similitud vectorial"""
        try:
            if not self.documents or not self.doc_embeddings:
                return []
            
            # Generar embedding para la consulta
            query_embedding = await self.generate_embedding(query)
            
            # Calcular similitudes
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np
            
            similarities = cosine_similarity([query_embedding], self.doc_embeddings)[0]
            
            # Obtener top-k documentos
            top_indices = np.argsort(similarities)[-top_k:][::-1]
            
            # Filtrar por umbral mínimo
            threshold = 0.1
            relevant_docs = []
            
            for idx in top_indices:
                if similarities[idx] >= threshold:
                    doc = self.documents[idx].copy()
                    doc['score'] = float(similarities[idx])
                    relevant_docs.append(doc)
            
            return relevant_docs
            
        except Exception as e:
            self.logger.error(f"Error en búsqueda: {e}")
            return []
    
    async def query(self, question: str, mode: str = "hybrid") -> str:
        """Procesa una consulta usando RAG"""
        try:
            self.logger.info(f"Procesando consulta: {question}")
            
            # Buscar documentos relevantes
            relevant_docs = await self.search_documents(question)
            
            # Construir contexto
            context = ""
            if relevant_docs:
                context_parts = []
                for i, doc in enumerate(relevant_docs[:3]):  # Top 3
                    # Truncar texto largo
                    text = doc['text'][:500] + "..." if len(doc['text']) > 500 else doc['text']
                    context_parts.append(f"Documento {i+1}: {text}")
                
                context = "\n\n".join(context_parts)
            
            # Construir prompt
            if context:
                system_prompt = "Eres un asistente útil que responde preguntas basándose en documentos proporcionados. Responde en español de manera clara y concisa."
                
                prompt = f"""Basándote en el siguiente contexto, responde la pregunta:

Contexto:
{context}

Pregunta: {question}

Respuesta:"""
            else:
                system_prompt = "Eres un asistente útil. Responde en español de manera clara y concisa."
                prompt = f"Pregunta: {question}\n\nRespuesta:"
            
            # Generar respuesta
            response = await self.generate_response(prompt, system_prompt)
            
            self.logger.info("Consulta procesada exitosamente")
            return response
            
        except Exception as e:
            self.logger.error(f"Error procesando consulta: {e}")
            return f"Error: {str(e)}"
    
    async def get_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas del agente"""
        return {
            "documents_loaded": len(self.documents),
            "embeddings_generated": len(self.doc_embeddings),
            "llm_model": self.llm_model,
            "embedding_model": self.embedding_model,
            "region": self.region,
            "profile": self.profile,
            "status": "active"
        }


# Instancia global
_bedrock_agent_instance = None

def get_bedrock_agent() -> BedrockRAGAgent:
    """Obtiene instancia del agente Bedrock"""
    global _bedrock_agent_instance
    if _bedrock_agent_instance is None:
        _bedrock_agent_instance = BedrockRAGAgent()
    return _bedrock_agent_instance
