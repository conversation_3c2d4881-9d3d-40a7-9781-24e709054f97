"""
Agente RAG simplificado usando componentes de LightRAG y modelos Hugging Face
"""
import os
import logging
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
import json

# Importar componentes de LightRAG
from lightrag.core import (
    Component, Generator, Embedder, Retriever, Document, 
    UserQuery, RetrieverOutput, GeneratorOutput
)

from .config import (
    LLM_CONFIG,
    EMBEDDING_CONFIG,
    LIGHTRAG_CONFIG,
    DOCS_DIR,
    WORKING_DIR,
    LOGGING_CONFIG,
    LOCAL_MODEL_CONFIG,
)


class HuggingFaceEmbedder(Embedder):
    """Embedder usando sentence-transformers"""
    
    def __init__(self):
        super().__init__()
        self.setup_model()
    
    def setup_model(self):
        """Configura el modelo de embeddings"""
        try:
            from sentence_transformers import SentenceTransformer
            
            cache_dir = LOCAL_MODEL_CONFIG["sentence_transformers"]["cache_dir"]
            os.makedirs(cache_dir, exist_ok=True)
            
            self.model = SentenceTransformer(
                EMBEDDING_CONFIG["model"],
                cache_folder=cache_dir
            )
            
            logging.info(f"Embedder cargado: {EMBEDDING_CONFIG['model']}")
            
        except Exception as e:
            logging.error(f"Error cargando embedder: {e}")
            raise
    
    async def acall(self, input_data) -> List[float]:
        """Genera embeddings para el texto"""
        try:
            if isinstance(input_data, str):
                text = input_data
            elif hasattr(input_data, 'text'):
                text = input_data.text
            else:
                text = str(input_data)
            
            # Generar embedding
            embedding = self.model.encode([text])[0]
            return embedding.tolist()
            
        except Exception as e:
            logging.error(f"Error generando embedding: {e}")
            return [0.0] * EMBEDDING_CONFIG["dimensions"]


class HuggingFaceGenerator(Generator):
    """Generador usando modelos Hugging Face"""
    
    def __init__(self):
        super().__init__()
        self.setup_model()
    
    def setup_model(self):
        """Configura el modelo de generación"""
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            model_name = LLM_CONFIG["model"]
            cache_dir = LOCAL_MODEL_CONFIG["transformers"]["cache_dir"]
            os.makedirs(cache_dir, exist_ok=True)
            
            # Cargar tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                padding_side="left"
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Cargar modelo
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                low_cpu_mem_usage=True
            )
            
            if torch.cuda.is_available():
                self.model = self.model.cuda()
            
            logging.info(f"Generador cargado: {model_name}")
            
        except Exception as e:
            logging.error(f"Error cargando generador: {e}")
            raise
    
    async def acall(self, input_data) -> GeneratorOutput:
        """Genera respuesta usando el modelo"""
        try:
            import torch
            
            # Extraer prompt
            if hasattr(input_data, 'prompt'):
                prompt = input_data.prompt
            elif isinstance(input_data, str):
                prompt = input_data
            else:
                prompt = str(input_data)
            
            # Formatear prompt para DialoGPT
            if "DialoGPT" in LLM_CONFIG["model"]:
                formatted_prompt = f"User: {prompt}\nBot:"
            else:
                formatted_prompt = f"Question: {prompt}\nAnswer:"
            
            # Tokenizar
            inputs = self.tokenizer.encode(
                formatted_prompt,
                return_tensors="pt",
                max_length=512,
                truncation=True
            )
            
            if torch.cuda.is_available():
                inputs = inputs.cuda()
            
            # Generar
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=200,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.1,
                    no_repeat_ngram_size=3
                )
            
            # Decodificar respuesta
            new_tokens = outputs[0][inputs.shape[1]:]
            response = self.tokenizer.decode(new_tokens, skip_special_tokens=True)
            
            # Limpiar respuesta
            response = response.strip()
            if response.startswith("Bot:"):
                response = response[4:].strip()
            
            if len(response) > 500:
                response = response[:500] + "..."
            
            if not response:
                response = "Lo siento, no pude generar una respuesta."
            
            return GeneratorOutput(data=response)
            
        except Exception as e:
            logging.error(f"Error generando respuesta: {e}")
            return GeneratorOutput(data=f"Error: {str(e)}")


class SimpleVectorRetriever(Retriever):
    """Retriever simple usando búsqueda vectorial"""
    
    def __init__(self, embedder: HuggingFaceEmbedder):
        super().__init__()
        self.embedder = embedder
        self.documents = []
        self.embeddings = []
    
    async def add_documents(self, docs: List[Document]):
        """Agrega documentos al índice"""
        try:
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np
            
            for doc in docs:
                # Generar embedding para el documento
                embedding = await self.embedder.acall(doc.text)
                
                self.documents.append(doc)
                self.embeddings.append(embedding)
            
            logging.info(f"Agregados {len(docs)} documentos al índice")
            
        except Exception as e:
            logging.error(f"Error agregando documentos: {e}")
    
    async def acall(self, query: UserQuery) -> RetrieverOutput:
        """Busca documentos relevantes"""
        try:
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np
            
            if not self.documents:
                return RetrieverOutput(documents=[])
            
            # Generar embedding para la consulta
            query_embedding = await self.embedder.acall(query.query)
            
            # Calcular similitudes
            similarities = cosine_similarity(
                [query_embedding], 
                self.embeddings
            )[0]
            
            # Obtener top-k documentos
            top_k = 3
            top_indices = np.argsort(similarities)[-top_k:][::-1]
            
            # Filtrar por umbral mínimo
            threshold = 0.1
            relevant_docs = []
            
            for idx in top_indices:
                if similarities[idx] >= threshold:
                    doc = self.documents[idx]
                    doc.score = float(similarities[idx])
                    relevant_docs.append(doc)
            
            return RetrieverOutput(documents=relevant_docs)
            
        except Exception as e:
            logging.error(f"Error en búsqueda: {e}")
            return RetrieverOutput(documents=[])


class SimpleRAGAgent:
    """Agente RAG simplificado"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Inicializar componentes
        self.embedder = HuggingFaceEmbedder()
        self.generator = HuggingFaceGenerator()
        self.retriever = SimpleVectorRetriever(self.embedder)
        
        self.logger.info("Agente RAG simplificado inicializado")
    
    def setup_logging(self):
        """Configura logging"""
        os.makedirs(WORKING_DIR, exist_ok=True)
        
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG["level"]),
            format=LOGGING_CONFIG["format"],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG["file"]),
                logging.StreamHandler()
            ]
        )
    
    async def load_documents(self, docs_path: Optional[Path] = None) -> bool:
        """Carga documentos"""
        try:
            if docs_path is None:
                docs_path = DOCS_DIR
            
            self.logger.info(f"Cargando documentos desde: {docs_path}")
            
            # Buscar archivos
            supported_extensions = {'.md', '.txt', '.json'}
            documents = []
            
            for file_path in docs_path.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                            # Crear documento
                            doc = Document(
                                text=content,
                                meta_data={"source": str(file_path)}
                            )
                            documents.append(doc)
                            
                    except Exception as e:
                        self.logger.warning(f"Error leyendo {file_path}: {e}")
            
            # Agregar al retriever
            await self.retriever.add_documents(documents)
            
            self.logger.info(f"Cargados {len(documents)} documentos")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cargando documentos: {e}")
            return False
    
    async def query(self, question: str, mode: str = "vector") -> str:
        """Procesa una consulta"""
        try:
            self.logger.info(f"Procesando consulta: {question}")
            
            # Crear query
            user_query = UserQuery(query=question)
            
            # Buscar documentos relevantes
            retriever_output = await self.retriever.acall(user_query)
            
            # Construir contexto
            context = ""
            if retriever_output.documents:
                context = "\n\n".join([
                    f"Documento {i+1}: {doc.text[:300]}..."
                    for i, doc in enumerate(retriever_output.documents[:3])
                ])
            
            # Construir prompt
            if context:
                prompt = f"""Basándote en el siguiente contexto, responde la pregunta en español:

Contexto:
{context}

Pregunta: {question}

Respuesta:"""
            else:
                prompt = f"Pregunta: {question}\nRespuesta:"
            
            # Generar respuesta
            generator_output = await self.generator.acall(prompt)
            
            response = generator_output.data if hasattr(generator_output, 'data') else str(generator_output)
            
            self.logger.info("Consulta procesada exitosamente")
            return response
            
        except Exception as e:
            self.logger.error(f"Error procesando consulta: {e}")
            return f"Error: {str(e)}"
    
    async def get_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas del agente"""
        return {
            "documents_loaded": len(self.retriever.documents),
            "embedding_model": EMBEDDING_CONFIG["model"],
            "llm_model": LLM_CONFIG["model"],
            "status": "active"
        }


# Instancia global
_agent_instance = None

def get_simple_agent() -> SimpleRAGAgent:
    """Obtiene instancia del agente simple"""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = SimpleRAGAgent()
    return _agent_instance
