#!/usr/bin/env python3
"""
Script de prueba simplificado para modelos locales sin LightRAG
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


def test_sentence_transformers():
    """Prueba sentence-transformers"""
    print("🔤 Probando sentence-transformers...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # Cargar modelo ligero
        model = SentenceTransformer("all-MiniLM-L6-v2")
        
        # Probar embeddings
        texts = [
            "¿Qué es la inteligencia artificial?",
            "La IA es una rama de la informática",
            "Los modelos de lenguaje procesan texto"
        ]
        
        embeddings = model.encode(texts)
        
        print(f"✅ Modelo cargado: all-MiniLM-L6-v2")
        print(f"✅ Embeddings generados: {len(embeddings)} textos")
        print(f"✅ Dimensiones: {len(embeddings[0])}")
        
        # Calcular similitud
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        print(f"✅ Similitud entre textos 1-2: {similarity:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error con sentence-transformers: {e}")
        return False


def test_transformers():
    """Prueba transformers locales"""
    print("\n🤖 Probando transformers...")
    
    try:
        from transformers import pipeline
        
        # Usar un modelo pequeño para pruebas
        print("📦 Cargando modelo de texto...")
        generator = pipeline(
            "text-generation",
            model="distilgpt2",
            max_length=50,
            do_sample=True,
            temperature=0.7
        )
        
        # Generar texto
        prompt = "La inteligencia artificial es"
        result = generator(prompt, max_length=30, num_return_sequences=1)
        
        print(f"✅ Modelo cargado: distilgpt2")
        print(f"✅ Prompt: {prompt}")
        print(f"✅ Respuesta: {result[0]['generated_text']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error con transformers: {e}")
        return False


def test_ollama():
    """Prueba Ollama si está disponible"""
    print("\n🦙 Probando Ollama...")
    
    try:
        import ollama
        
        # Listar modelos disponibles
        models = ollama.list()
        
        if not models.get('models'):
            print("⚠️  No hay modelos descargados en Ollama")
            print("💡 Ejecuta: ./scripts/install_ollama.sh")
            return False
        
        print(f"✅ Ollama disponible con {len(models['models'])} modelos")
        
        # Usar el primer modelo disponible
        model_name = models['models'][0]['name']
        print(f"📦 Probando modelo: {model_name}")
        
        # Generar respuesta
        response = ollama.generate(
            model=model_name,
            prompt="¿Qué es la inteligencia artificial? Responde en español en máximo 30 palabras.",
            options={"num_predict": 50}
        )
        
        print(f"✅ Respuesta: {response['response'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error con Ollama: {e}")
        print("💡 Instala Ollama ejecutando: ./scripts/install_ollama.sh")
        return False


def test_config():
    """Prueba la configuración del agente"""
    print("\n⚙️  Probando configuración...")
    
    try:
        from agent.config import AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG
        
        print("📋 Modelos disponibles:")
        for provider, available in AVAILABLE_MODELS.items():
            status = "✅" if available else "❌"
            print(f"   {status} {provider}")
        
        print(f"\n🤖 LLM configurado:")
        print(f"   Proveedor: {LLM_CONFIG['provider']}")
        print(f"   Modelo: {LLM_CONFIG['model']}")
        
        print(f"\n🔤 Embeddings configurados:")
        print(f"   Proveedor: {EMBEDDING_CONFIG['provider']}")
        print(f"   Modelo: {EMBEDDING_CONFIG['model']}")
        print(f"   Dimensiones: {EMBEDDING_CONFIG['dimensions']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        return False


async def test_simple_rag():
    """Prueba un RAG simplificado sin LightRAG"""
    print("\n🔍 Probando RAG simplificado...")
    
    try:
        from sentence_transformers import SentenceTransformer
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        # Cargar modelo de embeddings
        embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
        
        # Documentos de ejemplo
        documents = [
            "LightRAG es un framework para Retrieval-Augmented Generation",
            "El agente utiliza bases de datos vectoriales para búsquedas semánticas",
            "Los modelos locales permiten desarrollo sin APIs pagadas",
            "Sentence-transformers genera embeddings de alta calidad",
            "Ollama permite ejecutar modelos de lenguaje localmente"
        ]
        
        # Generar embeddings de documentos
        doc_embeddings = embedding_model.encode(documents)
        
        # Consulta de prueba
        query = "¿Cómo funciona la búsqueda semántica?"
        query_embedding = embedding_model.encode([query])
        
        # Calcular similitudes
        similarities = cosine_similarity(query_embedding, doc_embeddings)[0]
        
        # Encontrar documento más relevante
        best_idx = np.argmax(similarities)
        best_doc = documents[best_idx]
        best_score = similarities[best_idx]
        
        print(f"✅ Consulta: {query}")
        print(f"✅ Documento más relevante: {best_doc}")
        print(f"✅ Puntuación de similitud: {best_score:.3f}")
        
        # Generar respuesta simple
        response = f"Basado en la documentación: {best_doc}. La búsqueda semántica utiliza embeddings para encontrar contenido relevante por significado, no solo por palabras clave."
        
        print(f"✅ Respuesta generada: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en RAG simplificado: {e}")
        return False


def main():
    """Función principal"""
    print("🚀 Probando modelos locales para LightRAG Agent\n")
    
    results = {}
    
    # Probar sentence-transformers
    results['sentence_transformers'] = test_sentence_transformers()
    
    # Probar transformers
    results['transformers'] = test_transformers()
    
    # Probar Ollama
    results['ollama'] = test_ollama()
    
    # Probar configuración
    results['config'] = test_config()
    
    # Probar RAG simplificado
    results['simple_rag'] = asyncio.run(test_simple_rag())
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests >= 3:
        print("🎉 ¡Configuración básica funcionando!")
        print("\n💡 Próximos pasos:")
        print("1. Para mejor rendimiento: ./scripts/install_ollama.sh")
        print("2. Probar agente completo: python test_agent.py")
        print("3. Modo interactivo: python -m agent.main")
    else:
        print("⚠️  Algunos componentes necesitan configuración adicional")
        print("\n🔧 Soluciones:")
        if not results['sentence_transformers']:
            print("- Instalar sentence-transformers: pip install sentence-transformers")
        if not results['transformers']:
            print("- Instalar transformers: pip install transformers torch")
        if not results['ollama']:
            print("- Instalar Ollama: ./scripts/install_ollama.sh")


if __name__ == "__main__":
    main()
