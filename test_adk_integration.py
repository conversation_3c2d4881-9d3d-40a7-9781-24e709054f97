#!/usr/bin/env python3
"""
Test de integración Google ADK con modelos Hugging Face
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


class HuggingFaceRAGBackend:
    """Backend RAG usando modelos Hugging Face para ADK"""
    
    def __init__(self):
        self.embedding_model = None
        self.llm_tokenizer = None
        self.llm_model = None
        self.documents = []
        self.doc_embeddings = []
        self.setup_models()
    
    def setup_models(self):
        """Configura los modelos"""
        try:
            # Embeddings
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
            
            # LLM
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            model_name = "microsoft/DialoGPT-medium"
            self.llm_tokenizer = AutoTokenizer.from_pretrained(model_name, padding_side="left")
            
            if self.llm_tokenizer.pad_token is None:
                self.llm_tokenizer.pad_token = self.llm_tokenizer.eos_token
            
            self.llm_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                low_cpu_mem_usage=True
            )
            
            if torch.cuda.is_available():
                self.llm_model = self.llm_model.cuda()
            
            print("✅ Backend RAG inicializado con modelos Hugging Face")
            
        except Exception as e:
            print(f"❌ Error configurando backend: {e}")
            raise
    
    async def load_documents(self, docs_path: Path):
        """Carga documentos"""
        try:
            from sklearn.metrics.pairwise import cosine_similarity
            
            # Leer documentos
            supported_extensions = {'.md', '.txt', '.json'}
            self.documents = []
            
            for file_path in docs_path.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        self.documents.append({
                            'text': content,
                            'source': str(file_path)
                        })
            
            # Generar embeddings
            if self.documents:
                texts = [doc['text'] for doc in self.documents]
                self.doc_embeddings = self.embedding_model.encode(texts)
                print(f"✅ Cargados {len(self.documents)} documentos con embeddings")
            
            return len(self.documents) > 0
            
        except Exception as e:
            print(f"❌ Error cargando documentos: {e}")
            return False
    
    async def query(self, question: str, mode: str = "hybrid"):
        """Procesa consulta"""
        try:
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np
            import torch
            
            if not self.documents:
                return "No hay documentos cargados."
            
            # Búsqueda vectorial
            query_embedding = self.embedding_model.encode([question])
            similarities = cosine_similarity(query_embedding, self.doc_embeddings)[0]
            
            # Top-2 documentos
            top_indices = np.argsort(similarities)[-2:][::-1]
            relevant_docs = [
                self.documents[i]['text'][:300] + "..."
                for i in top_indices 
                if similarities[i] > 0.1
            ]
            
            # Construir contexto
            context = "\n\n".join(relevant_docs) if relevant_docs else ""
            
            # Generar respuesta
            if context:
                prompt = f"""Basándote en el contexto, responde en español:

Contexto:
{context}

Pregunta: {question}
Respuesta:"""
            else:
                prompt = f"User: {question}\nBot:"
            
            # Tokenizar y generar
            inputs = self.llm_tokenizer.encode(
                prompt, 
                return_tensors="pt", 
                max_length=512, 
                truncation=True
            )
            
            if torch.cuda.is_available():
                inputs = inputs.cuda()
            
            with torch.no_grad():
                outputs = self.llm_model.generate(
                    inputs,
                    max_new_tokens=150,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.llm_tokenizer.pad_token_id,
                    eos_token_id=self.llm_tokenizer.eos_token_id,
                    repetition_penalty=1.1
                )
            
            new_tokens = outputs[0][inputs.shape[1]:]
            response = self.llm_tokenizer.decode(new_tokens, skip_special_tokens=True)
            
            # Limpiar respuesta
            response = response.strip()
            if response.startswith("Bot:"):
                response = response[4:].strip()
            
            if len(response) > 400:
                response = response[:400] + "..."
            
            return response if response else "No pude generar una respuesta."
            
        except Exception as e:
            print(f"❌ Error en consulta: {e}")
            return f"Error: {str(e)}"
    
    async def get_stats(self):
        """Estadísticas"""
        return {
            "documents_loaded": len(self.documents),
            "embedding_model": "all-MiniLM-L6-v2",
            "llm_model": "microsoft/DialoGPT-medium",
            "status": "active"
        }


class LightRAGTool:
    """Herramienta ADK para consultas RAG"""
    
    def __init__(self, backend: HuggingFaceRAGBackend):
        self.name = "lightrag_query"
        self.description = "Consulta documentos usando RAG con modelos Hugging Face"
        self.backend = backend
        self.parameters = {
            "question": {
                "type": "string",
                "description": "Pregunta a realizar sobre los documentos",
                "required": True
            },
            "mode": {
                "type": "string", 
                "description": "Modo de búsqueda: naive, local, global, hybrid",
                "enum": ["naive", "local", "global", "hybrid"],
                "default": "hybrid"
            }
        }
    
    async def execute(self, question: str, mode: str = "hybrid"):
        """Ejecuta consulta"""
        try:
            response = await self.backend.query(question, mode)
            
            return {
                "success": True,
                "response": response,
                "mode": mode,
                "question": question,
                "tool": self.name
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "question": question,
                "tool": self.name
            }


class DocumentLoaderTool:
    """Herramienta ADK para cargar documentos"""
    
    def __init__(self, backend: HuggingFaceRAGBackend):
        self.name = "load_documents"
        self.description = "Carga documentos en la base de conocimiento"
        self.backend = backend
        self.parameters = {
            "docs_path": {
                "type": "string",
                "description": "Ruta a los documentos a cargar",
                "required": False
            }
        }
    
    async def execute(self, docs_path: str = None):
        """Carga documentos"""
        try:
            path = Path(docs_path) if docs_path else Path("docs")
            success = await self.backend.load_documents(path)
            
            return {
                "success": success,
                "message": "Documentos cargados exitosamente" if success else "Error cargando documentos",
                "docs_path": str(path),
                "tool": self.name
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tool": self.name
            }


class KnowledgeGraphTool:
    """Herramienta ADK para información del grafo"""
    
    def __init__(self, backend: HuggingFaceRAGBackend):
        self.name = "knowledge_graph_info"
        self.description = "Obtiene información sobre la base de conocimiento"
        self.backend = backend
        self.parameters = {}
    
    async def execute(self):
        """Obtiene información"""
        try:
            stats = await self.backend.get_stats()
            return {
                "success": True,
                "stats": stats,
                "tool": self.name
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tool": self.name
            }


class ADKAgent:
    """Agente ADK que integra las herramientas"""
    
    def __init__(self):
        self.backend = HuggingFaceRAGBackend()
        self.tools = {
            "lightrag_query": LightRAGTool(self.backend),
            "load_documents": DocumentLoaderTool(self.backend),
            "knowledge_graph_info": KnowledgeGraphTool(self.backend)
        }
    
    async def process_message(self, message: str):
        """Procesa mensaje usando herramientas"""
        try:
            # Determinar qué herramienta usar basándose en el mensaje
            if "cargar" in message.lower() or "load" in message.lower():
                tool = self.tools["load_documents"]
                result = await tool.execute()
            elif "estadísticas" in message.lower() or "stats" in message.lower() or "info" in message.lower():
                tool = self.tools["knowledge_graph_info"]
                result = await tool.execute()
            else:
                # Usar herramienta de consulta por defecto
                tool = self.tools["lightrag_query"]
                result = await tool.execute(message)
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": message
            }


async def test_backend():
    """Prueba el backend RAG"""
    print("🔧 Probando backend RAG...")
    
    try:
        backend = HuggingFaceRAGBackend()
        
        # Cargar documentos
        docs_path = Path("docs")
        success = await backend.load_documents(docs_path)
        print(f"✅ Documentos cargados: {success}")
        
        # Probar consulta
        response = await backend.query("¿Qué es LightRAG?")
        print(f"✅ Consulta de prueba: {response[:100]}...")
        
        # Estadísticas
        stats = await backend.get_stats()
        print(f"✅ Estadísticas: {stats}")
        
        return backend
        
    except Exception as e:
        print(f"❌ Error en backend: {e}")
        return None


async def test_adk_tools(backend):
    """Prueba las herramientas ADK"""
    print("\n🛠️  Probando herramientas ADK...")
    
    try:
        # LightRAGTool
        print("📝 Probando LightRAGTool...")
        tool = LightRAGTool(backend)
        result = await tool.execute("¿Cuáles son las funcionalidades principales del agente?")
        print(f"✅ LightRAGTool: {result['success']}")
        if result['success']:
            print(f"   Respuesta: {result['response'][:80]}...")
        
        # DocumentLoaderTool
        print("📚 Probando DocumentLoaderTool...")
        tool = DocumentLoaderTool(backend)
        result = await tool.execute()
        print(f"✅ DocumentLoaderTool: {result['success']}")
        print(f"   Mensaje: {result['message']}")
        
        # KnowledgeGraphTool
        print("📊 Probando KnowledgeGraphTool...")
        tool = KnowledgeGraphTool(backend)
        result = await tool.execute()
        print(f"✅ KnowledgeGraphTool: {result['success']}")
        if result['success']:
            print(f"   Stats: {result['stats']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en herramientas ADK: {e}")
        return False


async def test_adk_agent():
    """Prueba el agente ADK completo"""
    print("\n🤖 Probando agente ADK completo...")
    
    try:
        agent = ADKAgent()
        
        # Probar diferentes tipos de mensajes
        test_messages = [
            "cargar documentos",
            "¿Qué es LightRAG y cómo funciona?",
            "estadísticas del sistema",
            "¿Cuáles son las ventajas de los modelos locales?"
        ]
        
        for message in test_messages:
            print(f"\n📝 Mensaje: {message}")
            result = await agent.process_message(message)
            
            if result['success']:
                print(f"✅ Respuesta exitosa")
                if 'response' in result:
                    print(f"   Contenido: {result['response'][:80]}...")
                elif 'message' in result:
                    print(f"   Mensaje: {result['message']}")
                elif 'stats' in result:
                    print(f"   Stats: {result['stats']}")
            else:
                print(f"❌ Error: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en agente ADK: {e}")
        return False


async def main():
    """Función principal"""
    print("🚀 Test de integración Google ADK con Hugging Face\n")
    
    results = {}
    
    # 1. Backend RAG
    backend = await test_backend()
    results['backend'] = backend is not None
    
    if not backend:
        print("❌ Backend falló, abortando")
        return
    
    # 2. Herramientas ADK
    results['adk_tools'] = await test_adk_tools(backend)
    
    # 3. Agente ADK completo
    results['adk_agent'] = await test_adk_agent()
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests == total_tests:
        print("\n🎉 ¡Integración ADK con Hugging Face funcionando perfectamente!")
        print("\n✅ Funcionalidades verificadas:")
        print("- Backend RAG con modelos Hugging Face")
        print("- Herramientas ADK (LightRAG, DocumentLoader, KnowledgeGraph)")
        print("- Agente ADK completo con procesamiento de mensajes")
        print("- Búsqueda vectorial y generación de respuestas")
        print("- Carga de documentos y estadísticas")
        
        print("\n🚀 El sistema está listo para:")
        print("- Integración completa con Google ADK")
        print("- Procesamiento de consultas en lenguaje natural")
        print("- Gestión de documentos y base de conocimiento")
        print("- Funcionamiento sin APIs pagadas")
        
    else:
        print("⚠️  Algunas funcionalidades necesitan ajustes")


if __name__ == "__main__":
    asyncio.run(main())
