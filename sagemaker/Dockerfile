# Dockerfile para despliegue en AWS SageMaker
FROM python:3.12-slim

# Configurar variables de entorno
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Crear directorio de trabajo
WORKDIR /opt/ml/code

# Copiar archivos de requisitos
COPY requirements.txt .

# Instalar dependencias de Python
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código del agente
COPY agent/ ./agent/
COPY docs/ ./docs/
COPY sagemaker/inference.py .
COPY sagemaker/serve .

# Crear directorios necesarios
RUN mkdir -p /opt/ml/model /opt/ml/input /opt/ml/output

# Hacer ejecutable el script de servicio
RUN chmod +x serve

# Exponer puerto
EXPOSE 8080

# Comando por defecto
ENTRYPOINT ["python", "inference.py"]
