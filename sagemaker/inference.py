"""
Script de inferencia para AWS SageMaker
Soporta tanto modelos de OpenAI como modelos locales gratuitos
"""
import os
import json
import logging
import asyncio
from typing import Dict, Any
from flask import Flask, request, jsonify
import sys
from pathlib import Path

# Agregar el directorio del agente al path
sys.path.insert(0, '/opt/ml/code')

from agent.lightrag_agent import get_agent
from agent.adk_integration import create_lightrag_adk_agent
from agent.config import AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Crear aplicación Flask
app = Flask(__name__)

# Variables globales
lightrag_agent = None
adk_agent = None


async def initialize_agents():
    """Inicializa los agentes"""
    global lightrag_agent, adk_agent
    
    try:
        logger.info("Inicializando agentes...")
        
        # Inicializar agente LightRAG
        lightrag_agent = get_agent()
        
        # Cargar documentos
        docs_path = Path("/opt/ml/code/docs")
        if docs_path.exists():
            success = await lightrag_agent.load_documents(docs_path)
            if success:
                logger.info("Documentos cargados exitosamente")
            else:
                logger.warning("Error cargando documentos")
        
        # Inicializar agente ADK
        adk_agent = create_lightrag_adk_agent()
        
        logger.info("Agentes inicializados correctamente")
        return True
        
    except Exception as e:
        logger.error(f"Error inicializando agentes: {e}")
        return False


@app.route('/ping', methods=['GET'])
def ping():
    """Health check endpoint"""
    return jsonify({"status": "healthy"})


@app.route('/models', methods=['GET'])
def models_info():
    """Información sobre modelos disponibles"""
    return jsonify({
        "available_models": AVAILABLE_MODELS,
        "current_llm": {
            "provider": LLM_CONFIG["provider"],
            "model": LLM_CONFIG["model"]
        },
        "current_embedding": {
            "provider": EMBEDDING_CONFIG["provider"],
            "model": EMBEDDING_CONFIG["model"],
            "dimensions": EMBEDDING_CONFIG["dimensions"]
        }
    })


@app.route('/invocations', methods=['POST'])
def invocations():
    """Endpoint principal para inferencia"""
    try:
        # Obtener datos de la solicitud
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Extraer parámetros
        question = data.get('question', '')
        mode = data.get('mode', 'hybrid')
        use_adk = data.get('use_adk', False)
        
        if not question:
            return jsonify({"error": "Question is required"}), 400
        
        # Procesar consulta
        if use_adk and adk_agent:
            # Usar agente ADK
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            response = loop.run_until_complete(
                adk_agent.process_message(question)
            )
            loop.close()
        else:
            # Usar agente LightRAG directo
            if not lightrag_agent:
                return jsonify({"error": "Agent not initialized"}), 500
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            response = loop.run_until_complete(
                lightrag_agent.query(question, mode)
            )
            loop.close()
        
        return jsonify({
            "response": response,
            "question": question,
            "mode": mode,
            "use_adk": use_adk
        })
        
    except Exception as e:
        logger.error(f"Error en inferencia: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/load_documents', methods=['POST'])
def load_documents():
    """Endpoint para cargar documentos"""
    try:
        if not lightrag_agent:
            return jsonify({"error": "Agent not initialized"}), 500
        
        data = request.get_json()
        docs_path = data.get('docs_path') if data else None
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        path = Path(docs_path) if docs_path else Path("/opt/ml/code/docs")
        success = loop.run_until_complete(
            lightrag_agent.load_documents(path)
        )
        loop.close()
        
        return jsonify({
            "success": success,
            "message": "Documents loaded successfully" if success else "Error loading documents"
        })
        
    except Exception as e:
        logger.error(f"Error cargando documentos: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/graph_info', methods=['GET'])
def graph_info():
    """Endpoint para obtener información del grafo"""
    try:
        if not lightrag_agent:
            return jsonify({"error": "Agent not initialized"}), 500
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        info = loop.run_until_complete(
            lightrag_agent.get_knowledge_graph_info()
        )
        loop.close()
        
        return jsonify(info)
        
    except Exception as e:
        logger.error(f"Error obteniendo info del grafo: {e}")
        return jsonify({"error": str(e)}), 500


def model_fn(model_dir):
    """
    Función requerida por SageMaker para cargar el modelo
    """
    logger.info(f"Cargando modelo desde: {model_dir}")
    
    # Inicializar agentes de forma síncrona
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    success = loop.run_until_complete(initialize_agents())
    loop.close()
    
    if not success:
        raise Exception("Error inicializando agentes")
    
    return {"status": "loaded"}


def input_fn(request_body, request_content_type):
    """
    Función para procesar entrada
    """
    if request_content_type == 'application/json':
        return json.loads(request_body)
    else:
        raise ValueError(f"Unsupported content type: {request_content_type}")


def predict_fn(input_data, model):
    """
    Función de predicción
    """
    question = input_data.get('question', '')
    mode = input_data.get('mode', 'hybrid')
    use_adk = input_data.get('use_adk', False)
    
    if not question:
        raise ValueError("Question is required")
    
    # Procesar consulta
    if use_adk and adk_agent:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        response = loop.run_until_complete(
            adk_agent.process_message(question)
        )
        loop.close()
    else:
        if not lightrag_agent:
            raise Exception("Agent not initialized")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        response = loop.run_until_complete(
            lightrag_agent.query(question, mode)
        )
        loop.close()
    
    return {
        "response": response,
        "question": question,
        "mode": mode,
        "use_adk": use_adk
    }


def output_fn(prediction, accept):
    """
    Función para formatear salida
    """
    if accept == 'application/json':
        return json.dumps(prediction), accept
    else:
        raise ValueError(f"Unsupported accept type: {accept}")


if __name__ == '__main__':
    # Inicializar agentes
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    success = loop.run_until_complete(initialize_agents())
    loop.close()
    
    if not success:
        logger.error("Error inicializando agentes")
        sys.exit(1)
    
    # Ejecutar servidor Flask
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
