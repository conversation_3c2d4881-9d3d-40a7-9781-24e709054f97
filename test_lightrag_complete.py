#!/usr/bin/env python3
"""
Test completo del agente LightRAG con modelos Hugging Face
"""
import asyncio
import os
import sys
import json
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


async def test_configuration():
    """Prueba la configuración del agente"""
    print("🔧 Probando configuración...")
    
    try:
        from agent.config import AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG, LOCAL_MODEL_CONFIG
        
        print(f"✅ LLM Provider: {LLM_CONFIG['provider']}")
        print(f"✅ LLM Model: {LLM_CONFIG['model']}")
        print(f"✅ Embedding Provider: {EMBEDDING_CONFIG['provider']}")
        print(f"✅ Embedding Model: {EMBEDDING_CONFIG['model']}")
        
        # Verificar que transformers sea el proveedor primario
        assert LLM_CONFIG['provider'] == 'transformers', f"Expected transformers, got {LLM_CONFIG['provider']}"
        print("✅ Hugging Face transformers configurado como proveedor primario")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        return False


async def test_agent_initialization():
    """Prueba la inicialización del agente"""
    print("\n🚀 Probando inicialización del agente...")
    
    try:
        from agent.lightrag_agent import LightRAGAgent
        
        print("📦 Creando instancia del agente...")
        agent = LightRAGAgent()
        
        print("✅ Agente inicializado correctamente")
        print(f"✅ LLM Provider: {agent.llm_provider}")
        print(f"✅ Embedding Provider: {agent.embedding_provider}")
        
        # Verificar que los modelos se cargaron
        if hasattr(agent, 'llm_model'):
            print("✅ Modelo LLM cargado")
        if hasattr(agent, 'embedding_model'):
            print("✅ Modelo de embeddings cargado")
        
        return agent
        
    except Exception as e:
        print(f"❌ Error inicializando agente: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_document_loading(agent):
    """Prueba la carga de documentos"""
    print("\n📚 Probando carga de documentos...")
    
    try:
        docs_dir = Path("docs")
        if not docs_dir.exists():
            print("⚠️  Directorio docs no existe, creando documentos de prueba...")
            docs_dir.mkdir(exist_ok=True)
            
            # Crear documento de prueba
            test_doc = docs_dir / "test_doc.md"
            test_doc.write_text("""
# Documento de Prueba

## ¿Qué es LightRAG?
LightRAG es un framework avanzado para Retrieval-Augmented Generation que combina búsqueda vectorial y de grafos.

## Características principales
- Búsqueda semántica usando embeddings
- Grafo de conocimiento para relaciones
- Múltiples modos de consulta: naive, local, global, hybrid

## Arquitectura
El sistema utiliza:
1. Base de datos vectorial para embeddings
2. Base de datos de grafos para relaciones
3. Modelos de lenguaje para generación
""")
        
        # Contar documentos
        doc_files = list(docs_dir.rglob("*.md")) + list(docs_dir.rglob("*.txt"))
        print(f"📄 Encontrados {len(doc_files)} documentos")
        
        # Cargar documentos
        print("⬇️  Cargando documentos en LightRAG...")
        success = await agent.load_documents(docs_dir)
        
        if success:
            print("✅ Documentos cargados exitosamente")
        else:
            print("❌ Error cargando documentos")
        
        return success
        
    except Exception as e:
        print(f"❌ Error cargando documentos: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_vector_search(agent):
    """Prueba la búsqueda vectorial"""
    print("\n🔍 Probando búsqueda vectorial...")
    
    try:
        # Consultas de prueba
        test_queries = [
            "¿Qué es LightRAG?",
            "¿Cuáles son las características principales?",
            "¿Cómo funciona la arquitectura del sistema?"
        ]
        
        for query in test_queries:
            print(f"\n📝 Consulta: {query}")
            
            # Probar modo naive (solo vectorial)
            response = await agent.query(query, mode="naive")
            print(f"✅ Respuesta (naive): {response[:100]}...")
            
            # Verificar que la respuesta no esté vacía
            assert len(response.strip()) > 0, "Respuesta vacía"
        
        print("✅ Búsqueda vectorial funcionando correctamente")
        return True
        
    except Exception as e:
        print(f"❌ Error en búsqueda vectorial: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_graph_search(agent):
    """Prueba la búsqueda en grafo"""
    print("\n🕸️  Probando búsqueda en grafo...")
    
    try:
        # Consultas que requieren relaciones
        graph_queries = [
            "¿Cómo se relacionan los embeddings con el grafo de conocimiento?",
            "¿Qué conexiones hay entre la búsqueda vectorial y los modos de consulta?"
        ]
        
        for query in graph_queries:
            print(f"\n📝 Consulta: {query}")
            
            # Probar modo local (grafo local)
            response = await agent.query(query, mode="local")
            print(f"✅ Respuesta (local): {response[:100]}...")
            
            # Probar modo global (grafo global)
            response = await agent.query(query, mode="global")
            print(f"✅ Respuesta (global): {response[:100]}...")
        
        print("✅ Búsqueda en grafo funcionando correctamente")
        return True
        
    except Exception as e:
        print(f"❌ Error en búsqueda en grafo: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_hybrid_search(agent):
    """Prueba la búsqueda híbrida"""
    print("\n🔀 Probando búsqueda híbrida...")
    
    try:
        # Consultas complejas que se benefician del modo híbrido
        hybrid_queries = [
            "Explica la arquitectura completa de LightRAG y sus componentes",
            "¿Cómo se integran la búsqueda vectorial y de grafos en el sistema?"
        ]
        
        for query in hybrid_queries:
            print(f"\n📝 Consulta: {query}")
            
            # Probar modo híbrido
            response = await agent.query(query, mode="hybrid")
            print(f"✅ Respuesta (hybrid): {response[:150]}...")
            
            # Verificar que la respuesta sea sustancial
            assert len(response.strip()) > 50, "Respuesta muy corta"
        
        print("✅ Búsqueda híbrida funcionando correctamente")
        return True
        
    except Exception as e:
        print(f"❌ Error en búsqueda híbrida: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_adk_integration():
    """Prueba la integración con Google ADK"""
    print("\n🔗 Probando integración con Google ADK...")
    
    try:
        from agent.adk_integration import LightRAGTool, DocumentLoaderTool, KnowledgeGraphTool
        
        # Probar LightRAGTool
        print("🛠️  Probando LightRAGTool...")
        lightrag_tool = LightRAGTool()
        result = await lightrag_tool.execute("¿Qué es LightRAG?", "hybrid")
        
        assert result["success"], f"LightRAGTool falló: {result}"
        print(f"✅ LightRAGTool: {result['response'][:50]}...")
        
        # Probar DocumentLoaderTool
        print("🛠️  Probando DocumentLoaderTool...")
        doc_tool = DocumentLoaderTool()
        result = await doc_tool.execute()
        
        print(f"✅ DocumentLoaderTool: {result['message']}")
        
        # Probar KnowledgeGraphTool
        print("🛠️  Probando KnowledgeGraphTool...")
        graph_tool = KnowledgeGraphTool()
        result = await graph_tool.execute()
        
        print(f"✅ KnowledgeGraphTool: {result}")
        
        print("✅ Integración ADK funcionando correctamente")
        return True
        
    except Exception as e:
        print(f"❌ Error en integración ADK: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_knowledge_graph_info(agent):
    """Prueba la información del grafo de conocimiento"""
    print("\n📊 Probando información del grafo...")
    
    try:
        info = await agent.get_knowledge_graph_info()
        print(f"✅ Estado del grafo: {info}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error obteniendo info del grafo: {e}")
        return False


async def main():
    """Función principal de pruebas"""
    print("🚀 Iniciando pruebas completas del agente LightRAG con Hugging Face\n")
    
    results = {}
    
    # 1. Probar configuración
    results['config'] = await test_configuration()
    
    if not results['config']:
        print("❌ Configuración falló, abortando pruebas")
        return
    
    # 2. Inicializar agente
    agent = await test_agent_initialization()
    results['initialization'] = agent is not None
    
    if not agent:
        print("❌ Inicialización falló, abortando pruebas")
        return
    
    # 3. Cargar documentos
    results['document_loading'] = await test_document_loading(agent)
    
    if not results['document_loading']:
        print("❌ Carga de documentos falló, continuando con pruebas limitadas")
    
    # 4. Probar búsquedas
    results['vector_search'] = await test_vector_search(agent)
    results['graph_search'] = await test_graph_search(agent)
    results['hybrid_search'] = await test_hybrid_search(agent)
    
    # 5. Probar integración ADK
    results['adk_integration'] = await test_adk_integration()
    
    # 6. Probar info del grafo
    results['graph_info'] = await test_knowledge_graph_info(agent)
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests >= 5:
        print("🎉 ¡Agente LightRAG funcionando correctamente con Hugging Face!")
        print("\n💡 El agente está listo para:")
        print("- Procesamiento de documentos con embeddings locales")
        print("- Búsqueda vectorial y de grafos")
        print("- Generación de respuestas con modelos Hugging Face")
        print("- Integración con Google ADK")
    else:
        print("⚠️  Algunas funcionalidades necesitan ajustes")


if __name__ == "__main__":
    asyncio.run(main())
