import{j as t,_ as oe,d as nt}from"./ui-vendor-CeCm8EER.js";import{r as l,g as Aa,R as ot}from"./react-vendor-DEwriMA6.js";import{c as A,C as Je,F as lt,a as Ve,b as Ta,u as te,s as rt,d as S,U as Xe,S as ct,e as _a,B as O,X as Ra,f as st,g as ae,D as fe,h as Ee,i as xe,j as ve,k as ge,l as he,m as pt,n as dt,E as mt,T as Ma,I as qa,o as Ia,p as oa,q as ut,r as ft,t as xt,A as vt,v as gt,w as ht,x as bt,y as Ie,z as Le,G as yt,H as jt,J as ua,K as fa,R as wt,L as kt,M as Dt,N as Be,O as Ue}from"./feature-graph-yCDRht5b.js";const La=l.forwardRef(({className:e,...a},n)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:n,className:A("w-full caption-bottom text-sm",e),...a})}));La.displayName="Table";const Ba=l.forwardRef(({className:e,...a},n)=>t.jsx("thead",{ref:n,className:A("[&_tr]:border-b",e),...a}));Ba.displayName="TableHeader";const Ua=l.forwardRef(({className:e,...a},n)=>t.jsx("tbody",{ref:n,className:A("[&_tr:last-child]:border-0",e),...a}));Ua.displayName="TableBody";const Pt=l.forwardRef(({className:e,...a},n)=>t.jsx("tfoot",{ref:n,className:A("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",e),...a}));Pt.displayName="TableFooter";const Qe=l.forwardRef(({className:e,...a},n)=>t.jsx("tr",{ref:n,className:A("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...a}));Qe.displayName="TableRow";const Z=l.forwardRef(({className:e,...a},n)=>t.jsx("th",{ref:n,className:A("text-muted-foreground h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));Z.displayName="TableHead";const ee=l.forwardRef(({className:e,...a},n)=>t.jsx("td",{ref:n,className:A("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));ee.displayName="TableCell";const zt=l.forwardRef(({className:e,...a},n)=>t.jsx("caption",{ref:n,className:A("text-muted-foreground mt-4 text-sm",e),...a}));zt.displayName="TableCaption";function Nt({title:e,description:a,icon:n=lt,action:i,className:o,...r}){return t.jsxs(Je,{className:A("flex w-full flex-col items-center justify-center space-y-6 bg-transparent p-16",o),...r,children:[t.jsx("div",{className:"mr-4 shrink-0 rounded-full border border-dashed p-4",children:t.jsx(n,{className:"text-muted-foreground size-8","aria-hidden":"true"})}),t.jsxs("div",{className:"flex flex-col items-center gap-1.5 text-center",children:[t.jsx(Ve,{children:e}),a?t.jsx(Ta,{children:a}):null]}),i||null]})}var $e={exports:{}},He,xa;function Ct(){if(xa)return He;xa=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return He=e,He}var Ke,va;function Et(){if(va)return Ke;va=1;var e=Ct();function a(){}function n(){}return n.resetWarningCache=a,Ke=function(){function i(d,c,b,y,v,k){if(k!==e){var m=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw m.name="Invariant Violation",m}}i.isRequired=i;function o(){return i}var r={array:i,bigint:i,bool:i,func:i,number:i,object:i,string:i,symbol:i,any:i,arrayOf:o,element:i,elementType:i,instanceOf:o,node:i,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:a};return r.PropTypes=r,r},Ke}var ga;function St(){return ga||(ga=1,$e.exports=Et()()),$e.exports}var Ft=St();const z=Aa(Ft),Ot=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function se(e,a,n){const i=At(e),{webkitRelativePath:o}=e,r=typeof a=="string"?a:typeof o=="string"&&o.length>0?o:`./${e.name}`;return typeof i.path!="string"&&ha(i,"path",r),ha(i,"relativePath",r),i}function At(e){const{name:a}=e;if(a&&a.lastIndexOf(".")!==-1&&!e.type){const i=a.split(".").pop().toLowerCase(),o=Ot.get(i);o&&Object.defineProperty(e,"type",{value:o,writable:!1,configurable:!1,enumerable:!0})}return e}function ha(e,a,n){Object.defineProperty(e,a,{value:n,writable:!1,configurable:!1,enumerable:!0})}const Tt=[".DS_Store","Thumbs.db"];function _t(e){return oe(this,void 0,void 0,function*(){return ze(e)&&Rt(e.dataTransfer)?Lt(e.dataTransfer,e.type):Mt(e)?qt(e):Array.isArray(e)&&e.every(a=>"getFile"in a&&typeof a.getFile=="function")?It(e):[]})}function Rt(e){return ze(e)}function Mt(e){return ze(e)&&ze(e.target)}function ze(e){return typeof e=="object"&&e!==null}function qt(e){return Ze(e.target.files).map(a=>se(a))}function It(e){return oe(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>se(n))})}function Lt(e,a){return oe(this,void 0,void 0,function*(){if(e.items){const n=Ze(e.items).filter(o=>o.kind==="file");if(a!=="drop")return n;const i=yield Promise.all(n.map(Bt));return ba($a(i))}return ba(Ze(e.files).map(n=>se(n)))})}function ba(e){return e.filter(a=>Tt.indexOf(a.name)===-1)}function Ze(e){if(e===null)return[];const a=[];for(let n=0;n<e.length;n++){const i=e[n];a.push(i)}return a}function Bt(e){if(typeof e.webkitGetAsEntry!="function")return ya(e);const a=e.webkitGetAsEntry();return a&&a.isDirectory?Ha(a):ya(e,a)}function $a(e){return e.reduce((a,n)=>[...a,...Array.isArray(n)?$a(n):[n]],[])}function ya(e,a){return oe(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const r=yield e.getAsFileSystemHandle();if(r===null)throw new Error(`${e} is not a File`);if(r!==void 0){const d=yield r.getFile();return d.handle=r,se(d)}}const i=e.getAsFile();if(!i)throw new Error(`${e} is not a File`);return se(i,(n=a==null?void 0:a.fullPath)!==null&&n!==void 0?n:void 0)})}function Ut(e){return oe(this,void 0,void 0,function*(){return e.isDirectory?Ha(e):$t(e)})}function Ha(e){const a=e.createReader();return new Promise((n,i)=>{const o=[];function r(){a.readEntries(d=>oe(this,void 0,void 0,function*(){if(d.length){const c=Promise.all(d.map(Ut));o.push(c),r()}else try{const c=yield Promise.all(o);n(c)}catch(c){i(c)}}),d=>{i(d)})}r()})}function $t(e){return oe(this,void 0,void 0,function*(){return new Promise((a,n)=>{e.file(i=>{const o=se(i,e.fullPath);a(o)},i=>{n(i)})})})}var De={},ja;function Ht(){return ja||(ja=1,De.__esModule=!0,De.default=function(e,a){if(e&&a){var n=Array.isArray(a)?a:a.split(",");if(n.length===0)return!0;var i=e.name||"",o=(e.type||"").toLowerCase(),r=o.replace(/\/.*$/,"");return n.some(function(d){var c=d.trim().toLowerCase();return c.charAt(0)==="."?i.toLowerCase().endsWith(c):c.endsWith("/*")?r===c.replace(/\/.*$/,""):o===c})}return!0}),De}var Kt=Ht();const We=Aa(Kt);function wa(e){return Yt(e)||Gt(e)||Wa(e)||Wt()}function Wt(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gt(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Yt(e){if(Array.isArray(e))return ea(e)}function ka(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,i)}return n}function Da(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?ka(Object(n),!0).forEach(function(i){Ka(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ka(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function Ka(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function ue(e,a){return Xt(e)||Vt(e,a)||Wa(e,a)||Jt()}function Jt(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Wa(e,a){if(e){if(typeof e=="string")return ea(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ea(e,a)}}function ea(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,i=new Array(a);n<a;n++)i[n]=e[n];return i}function Vt(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],o=!0,r=!1,d,c;try{for(n=n.call(e);!(o=(d=n.next()).done)&&(i.push(d.value),!(a&&i.length===a));o=!0);}catch(b){r=!0,c=b}finally{try{!o&&n.return!=null&&n.return()}finally{if(r)throw c}}return i}}function Xt(e){if(Array.isArray(e))return e}var Qt=typeof We=="function"?We:We.default,Zt="file-invalid-type",ei="file-too-large",ai="file-too-small",ti="too-many-files",ii=function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=a.split(","),i=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:Zt,message:"File type must be ".concat(i)}},Pa=function(a){return{code:ei,message:"File is larger than ".concat(a," ").concat(a===1?"byte":"bytes")}},za=function(a){return{code:ai,message:"File is smaller than ".concat(a," ").concat(a===1?"byte":"bytes")}},ni={code:ti,message:"Too many files"};function Ga(e,a){var n=e.type==="application/x-moz-file"||Qt(e,a);return[n,n?null:ii(a)]}function Ya(e,a,n){if(ne(e.size))if(ne(a)&&ne(n)){if(e.size>n)return[!1,Pa(n)];if(e.size<a)return[!1,za(a)]}else{if(ne(a)&&e.size<a)return[!1,za(a)];if(ne(n)&&e.size>n)return[!1,Pa(n)]}return[!0,null]}function ne(e){return e!=null}function oi(e){var a=e.files,n=e.accept,i=e.minSize,o=e.maxSize,r=e.multiple,d=e.maxFiles,c=e.validator;return!r&&a.length>1||r&&d>=1&&a.length>d?!1:a.every(function(b){var y=Ga(b,n),v=ue(y,1),k=v[0],m=Ya(b,i,o),C=ue(m,1),g=C[0],L=c?c(b):null;return k&&g&&!L})}function Ne(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Pe(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(a){return a==="Files"||a==="application/x-moz-file"}):!!e.target&&!!e.target.files}function Na(e){e.preventDefault()}function li(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function ri(e){return e.indexOf("Edge/")!==-1}function ci(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return li(e)||ri(e)}function V(){for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];return function(i){for(var o=arguments.length,r=new Array(o>1?o-1:0),d=1;d<o;d++)r[d-1]=arguments[d];return a.some(function(c){return!Ne(i)&&c&&c.apply(void 0,[i].concat(r)),Ne(i)})}}function si(){return"showOpenFilePicker"in window}function pi(e){if(ne(e)){var a=Object.entries(e).filter(function(n){var i=ue(n,2),o=i[0],r=i[1],d=!0;return Ja(o)||(console.warn('Skipped "'.concat(o,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),d=!1),(!Array.isArray(r)||!r.every(Va))&&(console.warn('Skipped "'.concat(o,'" because an invalid file extension was provided.')),d=!1),d}).reduce(function(n,i){var o=ue(i,2),r=o[0],d=o[1];return Da(Da({},n),{},Ka({},r,d))},{});return[{description:"Files",accept:a}]}return e}function di(e){if(ne(e))return Object.entries(e).reduce(function(a,n){var i=ue(n,2),o=i[0],r=i[1];return[].concat(wa(a),[o],wa(r))},[]).filter(function(a){return Ja(a)||Va(a)}).join(",")}function mi(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function ui(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function Ja(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function Va(e){return/^.*\.[\w]+$/.test(e)}var fi=["children"],xi=["open"],vi=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],gi=["refKey","onChange","onClick"];function hi(e){return ji(e)||yi(e)||Xa(e)||bi()}function bi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yi(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ji(e){if(Array.isArray(e))return aa(e)}function Ge(e,a){return Di(e)||ki(e,a)||Xa(e,a)||wi()}function wi(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Xa(e,a){if(e){if(typeof e=="string")return aa(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return aa(e,a)}}function aa(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,i=new Array(a);n<a;n++)i[n]=e[n];return i}function ki(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],o=!0,r=!1,d,c;try{for(n=n.call(e);!(o=(d=n.next()).done)&&(i.push(d.value),!(a&&i.length===a));o=!0);}catch(b){r=!0,c=b}finally{try{!o&&n.return!=null&&n.return()}finally{if(r)throw c}}return i}}function Di(e){if(Array.isArray(e))return e}function Ca(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,i)}return n}function E(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?Ca(Object(n),!0).forEach(function(i){ta(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ca(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function ta(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function Ce(e,a){if(e==null)return{};var n=Pi(e,a),i,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)i=r[o],!(a.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}function Pi(e,a){if(e==null)return{};var n={},i=Object.keys(e),o,r;for(r=0;r<i.length;r++)o=i[r],!(a.indexOf(o)>=0)&&(n[o]=e[o]);return n}var Se=l.forwardRef(function(e,a){var n=e.children,i=Ce(e,fi),o=zi(i),r=o.open,d=Ce(o,xi);return l.useImperativeHandle(a,function(){return{open:r}},[r]),ot.createElement(l.Fragment,null,n(E(E({},d),{},{open:r})))});Se.displayName="Dropzone";var Qa={disabled:!1,getFilesFromEvent:_t,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};Se.defaultProps=Qa;Se.propTypes={children:z.func,accept:z.objectOf(z.arrayOf(z.string)),multiple:z.bool,preventDropOnDocument:z.bool,noClick:z.bool,noKeyboard:z.bool,noDrag:z.bool,noDragEventsBubbling:z.bool,minSize:z.number,maxSize:z.number,maxFiles:z.number,disabled:z.bool,getFilesFromEvent:z.func,onFileDialogCancel:z.func,onFileDialogOpen:z.func,useFsAccessApi:z.bool,autoFocus:z.bool,onDragEnter:z.func,onDragLeave:z.func,onDragOver:z.func,onDrop:z.func,onDropAccepted:z.func,onDropRejected:z.func,onError:z.func,validator:z.func};var ia={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function zi(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=E(E({},Qa),e),n=a.accept,i=a.disabled,o=a.getFilesFromEvent,r=a.maxSize,d=a.minSize,c=a.multiple,b=a.maxFiles,y=a.onDragEnter,v=a.onDragLeave,k=a.onDragOver,m=a.onDrop,C=a.onDropAccepted,g=a.onDropRejected,L=a.onFileDialogCancel,f=a.onFileDialogOpen,N=a.useFsAccessApi,K=a.autoFocus,G=a.preventDropOnDocument,X=a.noClick,x=a.noKeyboard,j=a.noDrag,w=a.noDragEventsBubbling,T=a.onError,W=a.validator,F=l.useMemo(function(){return di(n)},[n]),ie=l.useMemo(function(){return pi(n)},[n]),U=l.useMemo(function(){return typeof f=="function"?f:Ea},[f]),q=l.useMemo(function(){return typeof L=="function"?L:Ea},[L]),M=l.useRef(null),$=l.useRef(null),p=l.useReducer(Ni,ia),D=Ge(p,2),P=D[0],h=D[1],B=P.isFocused,_=P.isFileDialogActive,H=l.useRef(typeof window<"u"&&window.isSecureContext&&N&&si()),pe=function(){!H.current&&_&&setTimeout(function(){if($.current){var u=$.current.files;u.length||(h({type:"closeDialog"}),q())}},300)};l.useEffect(function(){return window.addEventListener("focus",pe,!1),function(){window.removeEventListener("focus",pe,!1)}},[$,_,q,H]);var Q=l.useRef([]),de=function(u){M.current&&M.current.contains(u.target)||(u.preventDefault(),Q.current=[])};l.useEffect(function(){return G&&(document.addEventListener("dragover",Na,!1),document.addEventListener("drop",de,!1)),function(){G&&(document.removeEventListener("dragover",Na),document.removeEventListener("drop",de))}},[M,G]),l.useEffect(function(){return!i&&K&&M.current&&M.current.focus(),function(){}},[M,K,i]);var J=l.useCallback(function(s){T?T(s):console.error(s)},[T]),la=l.useCallback(function(s){s.preventDefault(),s.persist(),we(s),Q.current=[].concat(hi(Q.current),[s.target]),Pe(s)&&Promise.resolve(o(s)).then(function(u){if(!(Ne(s)&&!w)){var R=u.length,I=R>0&&oi({files:u,accept:F,minSize:d,maxSize:r,multiple:c,maxFiles:b,validator:W}),Y=R>0&&!I;h({isDragAccept:I,isDragReject:Y,isDragActive:!0,type:"setDraggedFiles"}),y&&y(s)}}).catch(function(u){return J(u)})},[o,y,J,w,F,d,r,c,b,W]),ra=l.useCallback(function(s){s.preventDefault(),s.persist(),we(s);var u=Pe(s);if(u&&s.dataTransfer)try{s.dataTransfer.dropEffect="copy"}catch{}return u&&k&&k(s),!1},[k,w]),ca=l.useCallback(function(s){s.preventDefault(),s.persist(),we(s);var u=Q.current.filter(function(I){return M.current&&M.current.contains(I)}),R=u.indexOf(s.target);R!==-1&&u.splice(R,1),Q.current=u,!(u.length>0)&&(h({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Pe(s)&&v&&v(s))},[M,v,w]),be=l.useCallback(function(s,u){var R=[],I=[];s.forEach(function(Y){var me=Ga(Y,F),ce=Ge(me,2),Oe=ce[0],Ae=ce[1],Te=Ya(Y,d,r),ke=Ge(Te,2),_e=ke[0],Re=ke[1],Me=W?W(Y):null;if(Oe&&_e&&!Me)R.push(Y);else{var qe=[Ae,Re];Me&&(qe=qe.concat(Me)),I.push({file:Y,errors:qe.filter(function(it){return it})})}}),(!c&&R.length>1||c&&b>=1&&R.length>b)&&(R.forEach(function(Y){I.push({file:Y,errors:[ni]})}),R.splice(0)),h({acceptedFiles:R,fileRejections:I,isDragReject:I.length>0,type:"setFiles"}),m&&m(R,I,u),I.length>0&&g&&g(I,u),R.length>0&&C&&C(R,u)},[h,c,F,d,r,b,m,C,g,W]),ye=l.useCallback(function(s){s.preventDefault(),s.persist(),we(s),Q.current=[],Pe(s)&&Promise.resolve(o(s)).then(function(u){Ne(s)&&!w||be(u,s)}).catch(function(u){return J(u)}),h({type:"reset"})},[o,be,J,w]),le=l.useCallback(function(){if(H.current){h({type:"openDialog"}),U();var s={multiple:c,types:ie};window.showOpenFilePicker(s).then(function(u){return o(u)}).then(function(u){be(u,null),h({type:"closeDialog"})}).catch(function(u){mi(u)?(q(u),h({type:"closeDialog"})):ui(u)?(H.current=!1,$.current?($.current.value=null,$.current.click()):J(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):J(u)});return}$.current&&(h({type:"openDialog"}),U(),$.current.value=null,$.current.click())},[h,U,q,N,be,J,ie,c]),sa=l.useCallback(function(s){!M.current||!M.current.isEqualNode(s.target)||(s.key===" "||s.key==="Enter"||s.keyCode===32||s.keyCode===13)&&(s.preventDefault(),le())},[M,le]),pa=l.useCallback(function(){h({type:"focus"})},[]),da=l.useCallback(function(){h({type:"blur"})},[]),ma=l.useCallback(function(){X||(ci()?setTimeout(le,0):le())},[X,le]),re=function(u){return i?null:u},Fe=function(u){return x?null:re(u)},je=function(u){return j?null:re(u)},we=function(u){w&&u.stopPropagation()},et=l.useMemo(function(){return function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=s.refKey,R=u===void 0?"ref":u,I=s.role,Y=s.onKeyDown,me=s.onFocus,ce=s.onBlur,Oe=s.onClick,Ae=s.onDragEnter,Te=s.onDragOver,ke=s.onDragLeave,_e=s.onDrop,Re=Ce(s,vi);return E(E(ta({onKeyDown:Fe(V(Y,sa)),onFocus:Fe(V(me,pa)),onBlur:Fe(V(ce,da)),onClick:re(V(Oe,ma)),onDragEnter:je(V(Ae,la)),onDragOver:je(V(Te,ra)),onDragLeave:je(V(ke,ca)),onDrop:je(V(_e,ye)),role:typeof I=="string"&&I!==""?I:"presentation"},R,M),!i&&!x?{tabIndex:0}:{}),Re)}},[M,sa,pa,da,ma,la,ra,ca,ye,x,j,i]),at=l.useCallback(function(s){s.stopPropagation()},[]),tt=l.useMemo(function(){return function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=s.refKey,R=u===void 0?"ref":u,I=s.onChange,Y=s.onClick,me=Ce(s,gi),ce=ta({accept:F,multiple:c,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:re(V(I,ye)),onClick:re(V(Y,at)),tabIndex:-1},R,$);return E(E({},ce),me)}},[$,n,c,ye,i]);return E(E({},P),{},{isFocused:B&&!i,getRootProps:et,getInputProps:tt,rootRef:M,inputRef:$,open:re(le)})}function Ni(e,a){switch(a.type){case"focus":return E(E({},e),{},{isFocused:!0});case"blur":return E(E({},e),{},{isFocused:!1});case"openDialog":return E(E({},ia),{},{isFileDialogActive:!0});case"closeDialog":return E(E({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return E(E({},e),{},{isDragActive:a.isDragActive,isDragAccept:a.isDragAccept,isDragReject:a.isDragReject});case"setFiles":return E(E({},e),{},{acceptedFiles:a.acceptedFiles,fileRejections:a.fileRejections,isDragReject:a.isDragReject});case"reset":return E({},ia);default:return e}}function Ea(){}function na(e,a={}){const{decimals:n=0,sizeType:i="normal"}=a,o=["Bytes","KB","MB","GB","TB"],r=["Bytes","KiB","MiB","GiB","TiB"];if(e===0)return"0 Byte";const d=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,d)).toFixed(n)} ${i==="accurate"?r[d]??"Bytes":o[d]??"Bytes"}`}function Ci(e){const{t:a}=te(),{value:n,onValueChange:i,onUpload:o,onReject:r,progresses:d,fileErrors:c,accept:b=rt,maxSize:y=1024*1024*200,maxFileCount:v=1,multiple:k=!1,disabled:m=!1,description:C,className:g,...L}=e,[f,N]=nt({prop:n,onChange:i}),K=l.useCallback((x,j)=>{const w=((f==null?void 0:f.length)??0)+x.length+j.length;if(!k&&v===1&&x.length+j.length>1){S.error(a("documentPanel.uploadDocuments.fileUploader.singleFileLimit"));return}if(w>v){S.error(a("documentPanel.uploadDocuments.fileUploader.maxFilesLimit",{count:v}));return}j.length>0&&(r?r(j):j.forEach(({file:U})=>{S.error(a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:U.name}))}));const T=x.map(U=>Object.assign(U,{preview:URL.createObjectURL(U)})),W=j.map(({file:U})=>Object.assign(U,{preview:URL.createObjectURL(U),rejected:!0})),F=[...T,...W],ie=f?[...f,...F]:F;if(N(ie),o&&x.length>0){const U=x.filter(q=>{var D;if(!q.name)return!1;const M=`.${((D=q.name.split(".").pop())==null?void 0:D.toLowerCase())||""}`,$=Object.entries(b||{}).some(([P,h])=>q.type===P||Array.isArray(h)&&h.includes(M)),p=q.size<=y;return $&&p});U.length>0&&o(U)}},[f,v,k,o,r,N,a,b,y]);function G(x){if(!f)return;const j=f.filter((w,T)=>T!==x);N(j),i==null||i(j)}l.useEffect(()=>()=>{f&&f.forEach(x=>{Za(x)&&URL.revokeObjectURL(x.preview)})},[]);const X=m||((f==null?void 0:f.length)??0)>=v;return t.jsxs("div",{className:"relative flex flex-col gap-6 overflow-hidden",children:[t.jsx(Se,{onDrop:K,noClick:!1,noKeyboard:!1,maxSize:y,maxFiles:v,multiple:v>1||k,disabled:X,validator:x=>{var T;if(!x.name)return{code:"invalid-file-name",message:a("documentPanel.uploadDocuments.fileUploader.invalidFileName",{fallback:"Invalid file name"})};const j=`.${((T=x.name.split(".").pop())==null?void 0:T.toLowerCase())||""}`;return Object.entries(b||{}).some(([W,F])=>x.type===W||Array.isArray(F)&&F.includes(j))?x.size>y?{code:"file-too-large",message:a("documentPanel.uploadDocuments.fileUploader.fileTooLarge",{maxSize:na(y)})}:null:{code:"file-invalid-type",message:a("documentPanel.uploadDocuments.fileUploader.unsupportedType")}},children:({getRootProps:x,getInputProps:j,isDragActive:w})=>t.jsxs("div",{...x(),className:A("group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition","ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none",w&&"border-muted-foreground/50",X&&"pointer-events-none opacity-60",g),...L,children:[t.jsx("input",{...j()}),w?t.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[t.jsx("div",{className:"rounded-full border border-dashed p-3",children:t.jsx(Xe,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),t.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dropHere")})]}):t.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[t.jsx("div",{className:"rounded-full border border-dashed p-3",children:t.jsx(Xe,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),t.jsxs("div",{className:"flex flex-col gap-px",children:[t.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dragAndDrop")}),C?t.jsx("p",{className:"text-muted-foreground/70 text-sm",children:C}):t.jsxs("p",{className:"text-muted-foreground/70 text-sm",children:[a("documentPanel.uploadDocuments.fileUploader.uploadDescription",{count:v,isMultiple:v===1/0,maxSize:na(y)}),a("documentPanel.uploadDocuments.fileTypes")]})]})]})]})}),f!=null&&f.length?t.jsx(ct,{className:"h-fit w-full px-3",children:t.jsx("div",{className:"flex max-h-48 flex-col gap-4",children:f==null?void 0:f.map((x,j)=>t.jsx(Ei,{file:x,onRemove:()=>G(j),progress:d==null?void 0:d[x.name],error:c==null?void 0:c[x.name]},j))})}):null]})}function Sa({value:e,error:a}){return t.jsx("div",{className:"relative h-2 w-full",children:t.jsx("div",{className:"h-full w-full overflow-hidden rounded-full bg-secondary",children:t.jsx("div",{className:A("h-full transition-all",a?"bg-red-400":"bg-primary"),style:{width:`${e}%`}})})})}function Ei({file:e,progress:a,error:n,onRemove:i}){const{t:o}=te();return t.jsxs("div",{className:"relative flex items-center gap-2.5",children:[t.jsxs("div",{className:"flex flex-1 gap-2.5",children:[n?t.jsx(_a,{className:"text-red-400 size-10","aria-hidden":"true"}):Za(e)?t.jsx(Si,{file:e}):null,t.jsxs("div",{className:"flex w-full flex-col gap-2",children:[t.jsxs("div",{className:"flex flex-col gap-px",children:[t.jsx("p",{className:"text-foreground/80 line-clamp-1 text-sm font-medium",children:e.name}),t.jsx("p",{className:"text-muted-foreground text-xs",children:na(e.size)})]}),n?t.jsxs("div",{className:"text-red-400 text-sm",children:[t.jsx("div",{className:"relative mb-2",children:t.jsx(Sa,{value:100,error:!0})}),t.jsx("p",{children:n})]}):a?t.jsx(Sa,{value:a}):null]})]}),t.jsx("div",{className:"flex items-center gap-2",children:t.jsxs(O,{type:"button",variant:"outline",size:"icon",className:"size-7",onClick:i,children:[t.jsx(Ra,{className:"size-4","aria-hidden":"true"}),t.jsx("span",{className:"sr-only",children:o("documentPanel.uploadDocuments.fileUploader.removeFile")})]})})]})}function Za(e){return"preview"in e&&typeof e.preview=="string"}function Si({file:e}){return e.type.startsWith("image/")?t.jsx("div",{className:"aspect-square shrink-0 rounded-md object-cover"}):t.jsx(_a,{className:"text-muted-foreground size-10","aria-hidden":"true"})}function Fi({onDocumentsUploaded:e}){const{t:a}=te(),[n,i]=l.useState(!1),[o,r]=l.useState(!1),[d,c]=l.useState({}),[b,y]=l.useState({}),v=l.useCallback(m=>{m.forEach(({file:C,errors:g})=>{var f;let L=((f=g[0])==null?void 0:f.message)||a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:C.name});L.includes("file-invalid-type")&&(L=a("documentPanel.uploadDocuments.fileUploader.unsupportedType")),c(N=>({...N,[C.name]:100})),y(N=>({...N,[C.name]:L}))})},[c,y,a]),k=l.useCallback(async m=>{var L,f;r(!0);let C=!1;y(N=>{const K={...N};return m.forEach(G=>{delete K[G.name]}),K});const g=S.loading(a("documentPanel.uploadDocuments.batch.uploading"));try{const N={},K=new Intl.Collator(["zh-CN","en"],{sensitivity:"accent",numeric:!0}),G=[...m].sort((x,j)=>K.compare(x.name,j.name));for(const x of G)try{c(w=>({...w,[x.name]:0}));const j=await st(x,w=>{console.debug(a("documentPanel.uploadDocuments.single.uploading",{name:x.name,percent:w})),c(T=>({...T,[x.name]:w}))});j.status==="duplicated"?(N[x.name]=a("documentPanel.uploadDocuments.fileUploader.duplicateFile"),y(w=>({...w,[x.name]:a("documentPanel.uploadDocuments.fileUploader.duplicateFile")}))):j.status!=="success"?(N[x.name]=j.message,y(w=>({...w,[x.name]:j.message}))):C=!0}catch(j){console.error(`Upload failed for ${x.name}:`,j);let w=ae(j);if(j&&typeof j=="object"&&"response"in j){const T=j;((L=T.response)==null?void 0:L.status)===400&&(w=((f=T.response.data)==null?void 0:f.detail)||w),c(W=>({...W,[x.name]:100}))}N[x.name]=w,y(T=>({...T,[x.name]:w}))}Object.keys(N).length>0?S.error(a("documentPanel.uploadDocuments.batch.error"),{id:g}):S.success(a("documentPanel.uploadDocuments.batch.success"),{id:g}),C&&e&&e().catch(x=>{console.error("Error refreshing documents:",x)})}catch(N){console.error("Unexpected error during upload:",N),S.error(a("documentPanel.uploadDocuments.generalError",{error:ae(N)}),{id:g})}finally{r(!1)}},[r,c,y,a,e]);return t.jsxs(fe,{open:n,onOpenChange:m=>{o||(m||(c({}),y({})),i(m))},children:[t.jsx(Ee,{asChild:!0,children:t.jsxs(O,{variant:"default",side:"bottom",tooltip:a("documentPanel.uploadDocuments.tooltip"),size:"sm",children:[t.jsx(Xe,{})," ",a("documentPanel.uploadDocuments.button")]})}),t.jsxs(xe,{className:"sm:max-w-xl",onCloseAutoFocus:m=>m.preventDefault(),children:[t.jsxs(ve,{children:[t.jsx(ge,{children:a("documentPanel.uploadDocuments.title")}),t.jsx(he,{children:a("documentPanel.uploadDocuments.description")})]}),t.jsx(Ci,{maxFileCount:1/0,maxSize:200*1024*1024,description:a("documentPanel.uploadDocuments.fileTypes"),onUpload:k,onReject:v,progresses:d,fileErrors:b,disabled:o})]})]})}const Fa=({htmlFor:e,className:a,children:n,...i})=>t.jsx("label",{htmlFor:e,className:a,...i,children:n});function Oi({onDocumentsCleared:e}){const{t:a}=te(),[n,i]=l.useState(!1),[o,r]=l.useState(""),[d,c]=l.useState(!1),b=o.toLowerCase()==="yes";l.useEffect(()=>{n||(r(""),c(!1))},[n]);const y=l.useCallback(async()=>{if(b)try{const v=await pt();if(v.status!=="success"){S.error(a("documentPanel.clearDocuments.failed",{message:v.message})),r("");return}if(S.success(a("documentPanel.clearDocuments.success")),d)try{await dt(),S.success(a("documentPanel.clearDocuments.cacheCleared"))}catch(k){S.error(a("documentPanel.clearDocuments.cacheClearFailed",{error:ae(k)}))}e&&e().catch(console.error),i(!1)}catch(v){S.error(a("documentPanel.clearDocuments.error",{error:ae(v)})),r("")}},[b,d,i,a,e]);return t.jsxs(fe,{open:n,onOpenChange:i,children:[t.jsx(Ee,{asChild:!0,children:t.jsxs(O,{variant:"outline",side:"bottom",tooltip:a("documentPanel.clearDocuments.tooltip"),size:"sm",children:[t.jsx(mt,{})," ",a("documentPanel.clearDocuments.button")]})}),t.jsxs(xe,{className:"sm:max-w-xl",onCloseAutoFocus:v=>v.preventDefault(),children:[t.jsxs(ve,{children:[t.jsxs(ge,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[t.jsx(Ma,{className:"h-5 w-5"}),a("documentPanel.clearDocuments.title")]}),t.jsx(he,{className:"pt-2",children:a("documentPanel.clearDocuments.description")})]}),t.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:a("documentPanel.clearDocuments.warning")}),t.jsx("div",{className:"mb-4",children:a("documentPanel.clearDocuments.confirm")}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"space-y-2",children:[t.jsx(Fa,{htmlFor:"confirm-text",className:"text-sm font-medium",children:a("documentPanel.clearDocuments.confirmPrompt")}),t.jsx(qa,{id:"confirm-text",value:o,onChange:v=>r(v.target.value),placeholder:a("documentPanel.clearDocuments.confirmPlaceholder"),className:"w-full"})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ia,{id:"clear-cache",checked:d,onCheckedChange:v=>c(v===!0)}),t.jsx(Fa,{htmlFor:"clear-cache",className:"text-sm font-medium cursor-pointer",children:a("documentPanel.clearDocuments.clearCache")})]})]}),t.jsxs(oa,{children:[t.jsx(O,{variant:"outline",onClick:()=>i(!1),children:a("common.cancel")}),t.jsx(O,{variant:"destructive",onClick:y,disabled:!b,children:a("documentPanel.clearDocuments.confirmButton")})]})]})]})}const Oa=({htmlFor:e,className:a,children:n,...i})=>t.jsx("label",{htmlFor:e,className:a,...i,children:n});function Ai({selectedDocIds:e,totalCompletedCount:a,onDocumentsDeleted:n}){const{t:i}=te(),[o,r]=l.useState(!1),[d,c]=l.useState(""),[b,y]=l.useState(!1),[v,k]=l.useState(!1),m=d.toLowerCase()==="yes"&&!v;l.useEffect(()=>{o||(c(""),y(!1),k(!1))},[o]);const C=l.useCallback(async()=>{if(!(!m||e.length===0)){if(e.length===a&&a>0){S.error(i("documentPanel.deleteDocuments.cannotDeleteAll"));return}k(!0);try{const g=await ut(e,b);if(g.status==="deletion_started")S.success(i("documentPanel.deleteDocuments.success",{count:e.length}));else if(g.status==="busy"){S.error(i("documentPanel.deleteDocuments.busy")),c(""),k(!1);return}else if(g.status==="not_allowed"){S.error(i("documentPanel.deleteDocuments.notAllowed")),c(""),k(!1);return}else{S.error(i("documentPanel.deleteDocuments.failed",{message:g.message})),c(""),k(!1);return}n&&n().catch(console.error),r(!1)}catch(g){S.error(i("documentPanel.deleteDocuments.error",{error:ae(g)})),c("")}finally{k(!1)}}},[m,e,a,b,r,i,n]);return t.jsxs(fe,{open:o,onOpenChange:r,children:[t.jsx(Ee,{asChild:!0,children:t.jsxs(O,{variant:"destructive",side:"bottom",tooltip:i("documentPanel.deleteDocuments.tooltip",{count:e.length}),size:"sm",children:[t.jsx(ft,{})," ",i("documentPanel.deleteDocuments.button")]})}),t.jsxs(xe,{className:"sm:max-w-xl",onCloseAutoFocus:g=>g.preventDefault(),children:[t.jsxs(ve,{children:[t.jsxs(ge,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[t.jsx(Ma,{className:"h-5 w-5"}),i("documentPanel.deleteDocuments.title")]}),t.jsx(he,{className:"pt-2",children:i("documentPanel.deleteDocuments.description",{count:e.length})})]}),t.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:i("documentPanel.deleteDocuments.warning")}),t.jsx("div",{className:"mb-4",children:i("documentPanel.deleteDocuments.confirm",{count:e.length})}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"space-y-2",children:[t.jsx(Oa,{htmlFor:"confirm-text",className:"text-sm font-medium",children:i("documentPanel.deleteDocuments.confirmPrompt")}),t.jsx(qa,{id:"confirm-text",value:d,onChange:g=>c(g.target.value),placeholder:i("documentPanel.deleteDocuments.confirmPlaceholder"),className:"w-full",disabled:v})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",id:"delete-file",checked:b,onChange:g=>y(g.target.checked),disabled:v,className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"}),t.jsx(Oa,{htmlFor:"delete-file",className:"text-sm font-medium cursor-pointer",children:i("documentPanel.deleteDocuments.deleteFileOption")})]})]}),t.jsxs(oa,{children:[t.jsx(O,{variant:"outline",onClick:()=>r(!1),disabled:v,children:i("common.cancel")}),t.jsx(O,{variant:"destructive",onClick:C,disabled:!m,children:i(v?"documentPanel.deleteDocuments.deleting":"documentPanel.deleteDocuments.confirmButton")})]})]})]})}function Ti({selectedCount:e,onDeselect:a}){const{t:n}=te(),[i,o]=l.useState(!1);l.useEffect(()=>{},[i]);const r=l.useCallback(()=>{a(),o(!1)},[a,o]);return t.jsxs(fe,{open:i,onOpenChange:o,children:[t.jsx(Ee,{asChild:!0,children:t.jsxs(O,{variant:"outline",side:"bottom",tooltip:n("documentPanel.deselectDocuments.tooltip"),size:"sm",children:[t.jsx(Ra,{})," ",n("documentPanel.deselectDocuments.button")]})}),t.jsxs(xe,{className:"sm:max-w-md",onCloseAutoFocus:d=>d.preventDefault(),children:[t.jsxs(ve,{children:[t.jsxs(ge,{className:"flex items-center gap-2",children:[t.jsx(xt,{className:"h-5 w-5"}),n("documentPanel.deselectDocuments.title")]}),t.jsx(he,{className:"pt-2",children:n("documentPanel.deselectDocuments.description",{count:e})})]}),t.jsxs(oa,{children:[t.jsx(O,{variant:"outline",onClick:()=>o(!1),children:n("common.cancel")}),t.jsx(O,{variant:"default",onClick:r,children:n("documentPanel.deselectDocuments.confirmButton")})]})]})]})}function _i({open:e,onOpenChange:a}){var k;const{t:n}=te(),[i,o]=l.useState(null),[r,d]=l.useState("center"),[c,b]=l.useState(!1),y=l.useRef(null);l.useEffect(()=>{e&&(d("center"),b(!1))},[e]),l.useEffect(()=>{const m=y.current;!m||c||(m.scrollTop=m.scrollHeight)},[i==null?void 0:i.history_messages,c]);const v=()=>{const m=y.current;if(!m)return;const C=Math.abs(m.scrollHeight-m.scrollTop-m.clientHeight)<1;b(!C)};return l.useEffect(()=>{if(!e)return;const m=async()=>{try{const g=await bt();o(g)}catch(g){S.error(n("documentPanel.pipelineStatus.errors.fetchFailed",{error:ae(g)}))}};m();const C=setInterval(m,2e3);return()=>clearInterval(C)},[e,n]),t.jsx(fe,{open:e,onOpenChange:a,children:t.jsxs(xe,{className:A("sm:max-w-[800px] transition-all duration-200 fixed",r==="left"&&"!left-[25%] !translate-x-[-50%] !mx-4",r==="center"&&"!left-1/2 !-translate-x-1/2",r==="right"&&"!left-[75%] !translate-x-[-50%] !mx-4"),children:[t.jsx(he,{className:"sr-only",children:i!=null&&i.job_name?`${n("documentPanel.pipelineStatus.jobName")}: ${i.job_name}, ${n("documentPanel.pipelineStatus.progress")}: ${i.cur_batch}/${i.batchs}`:n("documentPanel.pipelineStatus.noActiveJob")}),t.jsxs(ve,{className:"flex flex-row items-center",children:[t.jsx(ge,{className:"flex-1",children:n("documentPanel.pipelineStatus.title")}),t.jsxs("div",{className:"flex items-center gap-2 mr-8",children:[t.jsx(O,{variant:"ghost",size:"icon",className:A("h-6 w-6",r==="left"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>d("left"),children:t.jsx(vt,{className:"h-4 w-4"})}),t.jsx(O,{variant:"ghost",size:"icon",className:A("h-6 w-6",r==="center"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>d("center"),children:t.jsx(gt,{className:"h-4 w-4"})}),t.jsx(O,{variant:"ghost",size:"icon",className:A("h-6 w-6",r==="right"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>d("right"),children:t.jsx(ht,{className:"h-4 w-4"})})]})]}),t.jsxs("div",{className:"space-y-4 pt-4",children:[t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.busy"),":"]}),t.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.busy?"bg-green-500":"bg-gray-300"}`})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.requestPending"),":"]}),t.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.request_pending?"bg-green-500":"bg-gray-300"}`})]})]}),t.jsxs("div",{className:"rounded-md border p-3 space-y-2",children:[t.jsxs("div",{children:[n("documentPanel.pipelineStatus.jobName"),": ",(i==null?void 0:i.job_name)||"-"]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsxs("span",{children:[n("documentPanel.pipelineStatus.startTime"),": ",i!=null&&i.job_start?new Date(i.job_start).toLocaleString(void 0,{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}):"-"]}),t.jsxs("span",{children:[n("documentPanel.pipelineStatus.progress"),": ",i?`${i.cur_batch}/${i.batchs} ${n("documentPanel.pipelineStatus.unit")}`:"-"]})]})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.latestMessage"),":"]}),t.jsx("div",{className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 whitespace-pre-wrap break-words",children:(i==null?void 0:i.latest_message)||"-"})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.historyMessages"),":"]}),t.jsx("div",{ref:y,onScroll:v,className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 overflow-y-auto min-h-[7.5em] max-h-[40vh]",children:(k=i==null?void 0:i.history_messages)!=null&&k.length?i.history_messages.map((m,C)=>t.jsx("div",{className:"whitespace-pre-wrap break-words",children:m},C)):"-"})]})]})]})})}const Ye=(e,a=20)=>{if(!e.file_path||typeof e.file_path!="string"||e.file_path.trim()==="")return e.id;const n=e.file_path.split("/"),i=n[n.length-1];return!i||i.trim()===""?e.id:i.length>a?i.slice(0,a)+"...":i},Ri=`
/* Tooltip styles */
.tooltip-container {
  position: relative;
  overflow: visible !important;
}

.tooltip {
  position: fixed; /* Use fixed positioning to escape overflow constraints */
  z-index: 9999; /* Ensure tooltip appears above all other elements */
  max-width: 600px;
  white-space: normal;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(0, 0, 0, 0.95);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none; /* Prevent tooltip from interfering with mouse events */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.15s, visibility 0.15s;
}

.tooltip.visible {
  opacity: 1;
  visibility: visible;
}

.dark .tooltip {
  background-color: rgba(255, 255, 255, 0.95);
  color: black;
}

/* Position tooltip helper class */
.tooltip-helper {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}

@keyframes pulse {
  0% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
  50% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  100% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
}

.dark .pipeline-busy {
  animation: dark-pulse 2s infinite;
}

@keyframes dark-pulse {
  0% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  50% {
    background-color: rgb(255 0 0 / 0.3);
    border-color: rgb(255 0 0 / 0.6);
  }
  100% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
}

.pipeline-busy {
  animation: pulse 2s infinite;
  border: 1px solid;
}
`;function Li(){const e=l.useRef(!0);l.useEffect(()=>{e.current=!0;const p=()=>{e.current=!1};return window.addEventListener("beforeunload",p),()=>{e.current=!1,window.removeEventListener("beforeunload",p)}},[]);const[a,n]=l.useState(!1),{t:i,i18n:o}=te(),r=Ie.use.health(),d=Ie.use.pipelineBusy(),[c,b]=l.useState(null),y=Le.use.currentTab(),v=Le.use.showFileName(),k=Le.use.setShowFileName(),[m,C]=l.useState("updated_at"),[g,L]=l.useState("desc"),[f,N]=l.useState("all"),[K,G]=l.useState([]),X=K.length>0,x=l.useCallback((p,D)=>{G(P=>D?[...P,p]:P.filter(h=>h!==p))},[]),j=l.useCallback(()=>{G([])},[]),w=p=>{m===p?L(D=>D==="asc"?"desc":"asc"):(C(p),L("desc"))},T=l.useCallback(p=>[...p].sort((D,P)=>{let h,B;m==="id"&&v?(h=Ye(D),B=Ye(P)):m==="id"?(h=D.id,B=P.id):(h=new Date(D[m]).getTime(),B=new Date(P[m]).getTime());const _=g==="asc"?1:-1;return typeof h=="string"&&typeof B=="string"?_*h.localeCompare(B):_*(h>B?1:h<B?-1:0)}),[m,g,v]),W=l.useMemo(()=>{if(!c)return null;const p=[];return f==="all"?Object.entries(c.statuses).forEach(([D,P])=>{P.forEach(h=>{p.push({...h,status:D})})}):(c.statuses[f]||[]).forEach(P=>{p.push({...P,status:f})}),m&&g?T(p):p},[c,m,g,f,T]),F=l.useMemo(()=>{if(!c)return{all:0};const p={all:0};return Object.entries(c.statuses).forEach(([D,P])=>{p[D]=P.length,p.all+=P.length}),p},[c]),ie=l.useRef({processed:0,processing:0,pending:0,failed:0});l.useEffect(()=>{const p=document.createElement("style");return p.textContent=Ri,document.head.appendChild(p),()=>{document.head.removeChild(p)}},[]);const U=l.useRef(null);l.useEffect(()=>{if(!c)return;const p=()=>{document.querySelectorAll(".tooltip-container").forEach(B=>{const _=B.querySelector(".tooltip");if(!_||!_.classList.contains("visible"))return;const H=B.getBoundingClientRect();_.style.left=`${H.left}px`,_.style.top=`${H.top-5}px`,_.style.transform="translateY(-100%)"})},D=h=>{const _=h.target.closest(".tooltip-container");if(!_)return;const H=_.querySelector(".tooltip");H&&(H.classList.add("visible"),p())},P=h=>{const _=h.target.closest(".tooltip-container");if(!_)return;const H=_.querySelector(".tooltip");H&&H.classList.remove("visible")};return document.addEventListener("mouseover",D),document.addEventListener("mouseout",P),()=>{document.removeEventListener("mouseover",D),document.removeEventListener("mouseout",P)}},[c]);const q=l.useCallback(async()=>{try{if(!e.current)return;const p=await yt();if(!e.current)return;e.current&&(p&&p.statuses&&Object.values(p.statuses).reduce((P,h)=>P+h.length,0)>0?b(p):b(null))}catch(p){e.current&&S.error(i("documentPanel.documentManager.errors.loadFailed",{error:ae(p)}))}},[b,i]);l.useEffect(()=>{y==="documents"&&q()},[y,q]);const M=l.useCallback(async()=>{try{if(!e.current)return;const{status:p}=await jt();if(!e.current)return;S.message(p)}catch(p){e.current&&S.error(i("documentPanel.documentManager.errors.scanFailed",{error:ae(p)}))}},[i]);l.useEffect(()=>{if(y!=="documents"||!r)return;const p=setInterval(async()=>{try{e.current&&await q()}catch(D){e.current&&S.error(i("documentPanel.documentManager.errors.scanProgressFailed",{error:ae(D)}))}},5e3);return()=>{clearInterval(p)}},[r,q,i,y]),l.useEffect(()=>{var P,h,B,_,H,pe,Q,de;if(!c)return;const p={processed:((h=(P=c==null?void 0:c.statuses)==null?void 0:P.processed)==null?void 0:h.length)||0,processing:((_=(B=c==null?void 0:c.statuses)==null?void 0:B.processing)==null?void 0:_.length)||0,pending:((pe=(H=c==null?void 0:c.statuses)==null?void 0:H.pending)==null?void 0:pe.length)||0,failed:((de=(Q=c==null?void 0:c.statuses)==null?void 0:Q.failed)==null?void 0:de.length)||0};Object.keys(p).some(J=>p[J]!==ie.current[J])&&e.current&&Ie.getState().check(),ie.current=p},[c]);const $=l.useCallback(async()=>{G([]),await q()},[q]);return l.useEffect(()=>{},[m,g]),t.jsxs(Je,{className:"!rounded-none !overflow-hidden flex flex-col h-full min-h-0",children:[t.jsx(ua,{className:"py-2 px-6",children:t.jsx(Ve,{className:"text-lg",children:i("documentPanel.documentManager.title")})}),t.jsxs(fa,{className:"flex-1 flex flex-col min-h-0 overflow-auto",children:[t.jsxs("div",{className:"flex gap-2 mb-2",children:[t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(O,{variant:"outline",onClick:M,side:"bottom",tooltip:i("documentPanel.documentManager.scanTooltip"),size:"sm",children:[t.jsx(wt,{})," ",i("documentPanel.documentManager.scanButton")]}),t.jsxs(O,{variant:"outline",onClick:()=>n(!0),side:"bottom",tooltip:i("documentPanel.documentManager.pipelineStatusTooltip"),size:"sm",className:A(d&&"pipeline-busy"),children:[t.jsx(kt,{})," ",i("documentPanel.documentManager.pipelineStatusButton")]})]}),t.jsx("div",{className:"flex-1"}),X&&t.jsx(Ai,{selectedDocIds:K,totalCompletedCount:F.processed||0,onDocumentsDeleted:$}),X?t.jsx(Ti,{selectedCount:K.length,onDeselect:j}):t.jsx(Oi,{onDocumentsCleared:q}),t.jsx(Fi,{onDocumentsUploaded:q}),t.jsx(_i,{open:a,onOpenChange:n})]}),t.jsxs(Je,{className:"flex-1 flex flex-col border rounded-md min-h-0 mb-2",children:[t.jsxs(ua,{className:"flex-none py-2 px-4",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx(Ve,{children:i("documentPanel.documentManager.uploadedTitle")}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(Dt,{className:"h-4 w-4"}),t.jsxs("div",{className:"flex gap-1",dir:o.dir(),children:[t.jsxs(O,{size:"sm",variant:f==="all"?"secondary":"outline",onClick:()=>N("all"),className:A(f==="all"&&"bg-gray-100 dark:bg-gray-900 font-medium border border-gray-400 dark:border-gray-500 shadow-sm"),children:[i("documentPanel.documentManager.status.all")," (",F.all,")"]}),t.jsxs(O,{size:"sm",variant:f==="processed"?"secondary":"outline",onClick:()=>N("processed"),className:A(F.processed>0?"text-green-600":"text-gray-500",f==="processed"&&"bg-green-100 dark:bg-green-900/30 font-medium border border-green-400 dark:border-green-600 shadow-sm"),children:[i("documentPanel.documentManager.status.completed")," (",F.processed||0,")"]}),t.jsxs(O,{size:"sm",variant:f==="processing"?"secondary":"outline",onClick:()=>N("processing"),className:A(F.processing>0?"text-blue-600":"text-gray-500",f==="processing"&&"bg-blue-100 dark:bg-blue-900/30 font-medium border border-blue-400 dark:border-blue-600 shadow-sm"),children:[i("documentPanel.documentManager.status.processing")," (",F.processing||0,")"]}),t.jsxs(O,{size:"sm",variant:f==="pending"?"secondary":"outline",onClick:()=>N("pending"),className:A(F.pending>0?"text-yellow-600":"text-gray-500",f==="pending"&&"bg-yellow-100 dark:bg-yellow-900/30 font-medium border border-yellow-400 dark:border-yellow-600 shadow-sm"),children:[i("documentPanel.documentManager.status.pending")," (",F.pending||0,")"]}),t.jsxs(O,{size:"sm",variant:f==="failed"?"secondary":"outline",onClick:()=>N("failed"),className:A(F.failed>0?"text-red-600":"text-gray-500",f==="failed"&&"bg-red-100 dark:bg-red-900/30 font-medium border border-red-400 dark:border-red-600 shadow-sm"),children:[i("documentPanel.documentManager.status.failed")," (",F.failed||0,")"]})]})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("label",{htmlFor:"toggle-filename-btn",className:"text-sm text-gray-500",children:i("documentPanel.documentManager.fileNameLabel")}),t.jsx(O,{id:"toggle-filename-btn",variant:"outline",size:"sm",onClick:()=>k(!v),className:"border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800",children:i(v?"documentPanel.documentManager.hideButton":"documentPanel.documentManager.showButton")})]})]}),t.jsx(Ta,{"aria-hidden":"true",className:"hidden",children:i("documentPanel.documentManager.uploadedDescription")})]}),t.jsxs(fa,{className:"flex-1 relative p-0",ref:U,children:[!c&&t.jsx("div",{className:"absolute inset-0 p-0",children:t.jsx(Nt,{title:i("documentPanel.documentManager.emptyTitle"),description:i("documentPanel.documentManager.emptyDescription")})}),c&&t.jsx("div",{className:"absolute inset-0 flex flex-col p-0",children:t.jsx("div",{className:"absolute inset-[-1px] flex flex-col p-0 border rounded-md border-gray-200 dark:border-gray-700 overflow-hidden",children:t.jsxs(La,{className:"w-full",children:[t.jsx(Ba,{className:"sticky top-0 bg-background z-10 shadow-sm",children:t.jsxs(Qe,{className:"border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/75 shadow-[inset_0_-1px_0_rgba(0,0,0,0.1)]",children:[t.jsx(Z,{onClick:()=>w("id"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.id"),m==="id"&&t.jsx("span",{className:"ml-1",children:g==="asc"?t.jsx(Be,{size:14}):t.jsx(Ue,{size:14})})]})}),t.jsx(Z,{children:i("documentPanel.documentManager.columns.summary")}),t.jsx(Z,{children:i("documentPanel.documentManager.columns.status")}),t.jsx(Z,{children:i("documentPanel.documentManager.columns.length")}),t.jsx(Z,{children:i("documentPanel.documentManager.columns.chunks")}),t.jsx(Z,{onClick:()=>w("created_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.created"),m==="created_at"&&t.jsx("span",{className:"ml-1",children:g==="asc"?t.jsx(Be,{size:14}):t.jsx(Ue,{size:14})})]})}),t.jsx(Z,{onClick:()=>w("updated_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.updated"),m==="updated_at"&&t.jsx("span",{className:"ml-1",children:g==="asc"?t.jsx(Be,{size:14}):t.jsx(Ue,{size:14})})]})}),t.jsx(Z,{className:"w-16 text-center",children:i("documentPanel.documentManager.columns.select")})]})}),t.jsx(Ua,{className:"text-sm overflow-auto",children:W&&W.map(p=>t.jsxs(Qe,{children:[t.jsx(ee,{className:"truncate font-mono overflow-visible max-w-[250px]",children:v?t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:Ye(p,30)}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:p.file_path})]}),t.jsx("div",{className:"text-xs text-gray-500",children:p.id})]}):t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:p.id}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:p.file_path})]})}),t.jsx(ee,{className:"max-w-xs min-w-45 truncate overflow-visible",children:t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:p.content_summary}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:p.content_summary})]})}),t.jsxs(ee,{children:[p.status==="processed"&&t.jsx("span",{className:"text-green-600",children:i("documentPanel.documentManager.status.completed")}),p.status==="processing"&&t.jsx("span",{className:"text-blue-600",children:i("documentPanel.documentManager.status.processing")}),p.status==="pending"&&t.jsx("span",{className:"text-yellow-600",children:i("documentPanel.documentManager.status.pending")}),p.status==="failed"&&t.jsx("span",{className:"text-red-600",children:i("documentPanel.documentManager.status.failed")}),p.error&&t.jsx("span",{className:"ml-2 text-red-500",title:p.error,children:"⚠️"})]}),t.jsx(ee,{children:p.content_length??"-"}),t.jsx(ee,{children:p.chunks_count??"-"}),t.jsx(ee,{className:"truncate",children:new Date(p.created_at).toLocaleString()}),t.jsx(ee,{className:"truncate",children:new Date(p.updated_at).toLocaleString()}),t.jsx(ee,{className:"text-center",children:t.jsx(Ia,{checked:K.includes(p.id),onCheckedChange:D=>x(p.id,D===!0),disabled:p.status!=="processed",className:"mx-auto"})})]},p.id))})]})})})]})]})]})]})}export{Li as D};
