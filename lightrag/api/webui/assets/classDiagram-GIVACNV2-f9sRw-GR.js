import{s as a,c as s,a as e,C as t}from"./chunk-A2AXSNBT-9BBpDWtu.js";import{_ as i}from"./mermaid-vendor-DnUYjsdS.js";import"./chunk-RZ5BOZE2-Du3GUvfy.js";import"./feature-graph-yCDRht5b.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";var f={parser:e,get db(){return new t},renderer:s,styles:a,init:i(r=>{r.class||(r.class={}),r.class.arrowMarkerAbsolute=r.arrowMarkerAbsolute},"init")};export{f as diagram};
