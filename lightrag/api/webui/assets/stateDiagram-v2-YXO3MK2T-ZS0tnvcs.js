import{s as r,b as e,a,S as s}from"./chunk-AEK57VVT-CXtM_4Od.js";import{_ as i}from"./mermaid-vendor-DnUYjsdS.js";import"./chunk-RZ5BOZE2-Du3GUvfy.js";import"./feature-graph-yCDRht5b.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";var b={parser:a,get db(){return new s(2)},renderer:e,styles:r,init:i(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{b as diagram};
