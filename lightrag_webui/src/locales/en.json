{"settings": {"language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System"}, "header": {"documents": "Documents", "knowledgeGraph": "Knowledge Graph", "retrieval": "Retrieval", "api": "API", "projectRepository": "Project Repository", "logout": "Logout", "themeToggle": {"switchToLight": "Switch to light theme", "switchToDark": "Switch to dark theme"}}, "login": {"description": "Please enter your account and password to log in to the system", "username": "Username", "usernamePlaceholder": "Please input a username", "password": "Password", "passwordPlaceholder": "Please input a password", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "successMessage": "<PERSON><PERSON> succeeded", "errorEmptyFields": "Please enter your username and password", "errorInvalidCredentials": "<PERSON><PERSON> failed, please check username and password", "authDisabled": "Authentication is disabled. Using login free mode.", "guestMode": "Login Free"}, "common": {"cancel": "Cancel", "save": "Save", "saving": "Saving...", "saveFailed": "Save failed"}, "documentPanel": {"clearDocuments": {"button": "Clear", "tooltip": "Clear documents", "title": "Clear Documents", "description": "This will remove all documents from the system", "warning": "WARNING: This action will permanently delete all documents and cannot be undone!", "confirm": "Do you really want to clear all documents?", "confirmPrompt": "Type 'yes' to confirm this action", "confirmPlaceholder": "Type yes to confirm", "clearCache": "Clear LLM cache", "confirmButton": "YES", "success": "Documents cleared successfully", "cacheCleared": "<PERSON><PERSON> cleared successfully", "cacheClearFailed": "Failed to clear cache:\n{{error}}", "failed": "Clear Documents Failed:\n{{message}}", "error": "Clear Documents Failed:\n{{error}}"}, "deleteDocuments": {"button": "Delete", "tooltip": "Delete selected documents", "title": "Delete Documents", "description": "This will permanently delete the selected documents from the system", "warning": "WARNING: This action will permanently delete the selected documents and cannot be undone!", "confirm": "Do you really want to delete {{count}} selected document(s)?", "confirmPrompt": "Type 'yes' to confirm this action", "confirmPlaceholder": "Type yes to confirm", "confirmButton": "YES", "deleteFileOption": "Also delete uploaded files", "deleteFileTooltip": "Check this option to also delete the corresponding uploaded files on the server", "success": "Document deletion pipeline started successfully", "failed": "Delete Documents Failed:\n{{message}}", "error": "Delete Documents Failed:\n{{error}}", "busy": "Pi<PERSON><PERSON> is busy, please try again later", "notAllowed": "No permission to perform this operation", "cannotDeleteAll": "Cannot delete all documents. If you need to delete all documents, please use the Clear Documents feature."}, "deselectDocuments": {"button": "Deselect", "tooltip": "Deselect all selected documents", "title": "Deselect Documents", "description": "This will clear all selected documents ({{count}} selected)", "confirmButton": "Deselect All"}, "uploadDocuments": {"button": "Upload", "tooltip": "Upload documents", "title": "Upload Documents", "description": "Drag and drop your documents here or click to browse.", "single": {"uploading": "Uploading {{name}}: {{percent}}%", "success": "Upload Success:\n{{name}} uploaded successfully", "failed": "Upload Failed:\n{{name}}\n{{message}}", "error": "Upload Failed:\n{{name}}\n{{error}}"}, "batch": {"uploading": "Uploading files...", "success": "Files uploaded successfully", "error": "Some files failed to upload"}, "generalError": "Upload Failed\n{{error}}", "fileTypes": "Supported types: TXT, MD, DOCX, PDF, PPTX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS", "fileUploader": {"singleFileLimit": "Cannot upload more than 1 file at a time", "maxFilesLimit": "Cannot upload more than {{count}} files", "fileRejected": "File {{name}} was rejected", "unsupportedType": "Unsupported file type", "fileTooLarge": "File too large, maximum size is {{maxSize}}", "dropHere": "Drop the files here", "dragAndDrop": "Drag and drop files here, or click to select files", "removeFile": "Remove file", "uploadDescription": "You can upload {{isMultiple ? 'multiple' : count}} files (up to {{maxSize}} each)", "duplicateFile": "File name already exists in server cache"}}, "documentManager": {"title": "Document Management", "scanButton": "<PERSON><PERSON>", "scanTooltip": "Scan documents in input folder", "pipelineStatusButton": "Pipeline Status", "pipelineStatusTooltip": "View pipeline status", "uploadedTitle": "Uploaded Documents", "uploadedDescription": "List of uploaded documents and their statuses.", "emptyTitle": "No Documents", "emptyDescription": "There are no uploaded documents yet.", "columns": {"id": "ID", "summary": "Summary", "status": "Status", "length": "Length", "chunks": "Chunks", "created": "Created", "updated": "Updated", "metadata": "<PERSON><PERSON><PERSON>", "select": "Select"}, "status": {"all": "All", "completed": "Completed", "processing": "Processing", "pending": "Pending", "failed": "Failed"}, "errors": {"loadFailed": "Failed to load documents\n{{error}}", "scanFailed": "Failed to scan documents\n{{error}}", "scanProgressFailed": "Failed to get scan progress\n{{error}}"}, "fileNameLabel": "File Name", "showButton": "Show", "hideButton": "<PERSON>de", "showFileNameTooltip": "Show file name", "hideFileNameTooltip": "Hide file name"}, "pipelineStatus": {"title": "Pipeline Status", "busy": "Pipeline Busy", "requestPending": "Request Pending", "jobName": "Job Name", "startTime": "Start Time", "progress": "Progress", "unit": "batch", "latestMessage": "Latest Message", "historyMessages": "History Messages", "errors": {"fetchFailed": "Failed to get pipeline status\n{{error}}"}}}, "graphPanel": {"dataIsTruncated": "Graph data is truncated to Max Nodes", "statusDialog": {"title": "LightRAG Server Settings", "description": "View current system status and connection information"}, "legend": "Legend", "nodeTypes": {"person": "Person", "category": "Category", "geo": "Geographic", "location": "Location", "organization": "Organization", "event": "Event", "equipment": "Equipment", "weapon": "Weapon", "animal": "Animal", "unknown": "Unknown", "object": "Object", "group": "Group", "technology": "Technology"}, "sideBar": {"settings": {"settings": "Settings", "healthCheck": "Health Check", "showPropertyPanel": "Show Property Panel", "showSearchBar": "Show Search Bar", "showNodeLabel": "Show Node Label", "nodeDraggable": "Node Draggable", "showEdgeLabel": "Show Edge Label", "hideUnselectedEdges": "Hide Unselected Edges", "edgeEvents": "Edge Events", "maxQueryDepth": "Max Query <PERSON>", "maxNodes": "<PERSON>", "maxLayoutIterations": "Max Layout Iterations", "resetToDefault": "Reset to default", "edgeSizeRange": "Edge Size Range", "depth": "D", "max": "Max", "degree": "Degree", "apiKey": "API Key", "enterYourAPIkey": "Enter your API key", "save": "Save", "refreshLayout": "Refresh Layout"}, "zoomControl": {"zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetZoom": "Reset Zoom", "rotateCamera": "Clockwise Rotate", "rotateCameraCounterClockwise": "Counter-Clockwise Rotate"}, "layoutsControl": {"startAnimation": "Continue layout animation", "stopAnimation": "Stop layout animation", "layoutGraph": "Layout Graph", "layouts": {"Circular": "Circular", "Circlepack": "Circlepack", "Random": "Random", "Noverlaps": "<PERSON><PERSON><PERSON><PERSON>", "Force Directed": "Force Directed", "Force Atlas": "Force Atlas"}}, "fullScreenControl": {"fullScreen": "Full Screen", "windowed": "Windowed"}, "legendControl": {"toggleLegend": "Toggle Legend"}}, "statusIndicator": {"connected": "Connected", "disconnected": "Disconnected"}, "statusCard": {"unavailable": "Status information unavailable", "storageInfo": "Storage Info", "workingDirectory": "Working Directory", "inputDirectory": "Input Directory", "llmConfig": "LLM Configuration", "llmBinding": "LLM Binding", "llmBindingHost": "LLM Binding Host", "llmModel": "LLM Model", "maxTokens": "<PERSON>", "embeddingConfig": "Embedding Configuration", "embeddingBinding": "Embedding Binding", "embeddingBindingHost": "Embedding Binding Host", "embeddingModel": "Embedding Model", "storageConfig": "Storage Configuration", "kvStorage": "KV Storage", "docStatusStorage": "Doc Status Storage", "graphStorage": "Graph Storage", "vectorStorage": "Vector Storage"}, "propertiesView": {"editProperty": "Edit {{property}}", "editPropertyDescription": "Edit the property value in the text area below.", "errors": {"duplicateName": "Node name already exists", "updateFailed": "Failed to update node", "tryAgainLater": "Please try again later"}, "success": {"entityUpdated": "Node updated successfully", "relationUpdated": "Relation updated successfully"}, "node": {"title": "Node", "id": "ID", "labels": "Labels", "degree": "Degree", "properties": "Properties", "relationships": "Relations(within subgraph)", "expandNode": "Expand Node", "pruneNode": "<PERSON><PERSON><PERSON>", "deleteAllNodesError": "Refuse to delete all nodes in the graph", "nodesRemoved": "{{count}} nodes removed, including orphan nodes", "noNewNodes": "No expandable nodes found", "propertyNames": {"description": "Description", "entity_id": "Name", "entity_type": "Type", "source_id": "SrcID", "Neighbour": "Neigh", "file_path": "Source", "keywords": "Keys", "weight": "Weight"}}, "edge": {"title": "Relationship", "id": "ID", "type": "Type", "source": "Source", "target": "Target", "properties": "Properties"}}, "search": {"placeholder": "Search nodes...", "message": "And {count} others"}, "graphLabels": {"selectTooltip": "Select query label", "noLabels": "No labels found", "label": "Label", "placeholder": "Search labels...", "andOthers": "And {count} others", "refreshTooltip": "Reload data(After file added)"}, "emptyGraph": "Empty(Try Reload Again)"}, "retrievePanel": {"chatMessage": {"copyTooltip": "Copy to clipboard", "copyError": "Failed to copy text to clipboard"}, "retrieval": {"startPrompt": "Start a retrieval by typing your query below", "clear": "Clear", "send": "Send", "placeholder": "Enter your query (Support prefix: /<Query Mode>)", "error": "Error: Failed to get response", "queryModeError": "Only supports the following query modes: {{modes}}", "queryModePrefixInvalid": "Invalid query mode prefix. Use: /<mode> [space] your query"}, "querySettings": {"parametersTitle": "Parameters", "parametersDescription": "Configure your query parameters", "queryMode": "Query Mode", "queryModeTooltip": "Select the retrieval strategy:\n• Naive: Basic search without advanced techniques\n• Local: Context-dependent information retrieval\n• Global: Utilizes global knowledge base\n• Hybrid: Combines local and global retrieval\n• Mix: Integrates knowledge graph with vector retrieval\n• Bypass: Passes query directly to LLM without retrieval", "queryModeOptions": {"naive": "Naive", "local": "Local", "global": "Global", "hybrid": "Hybrid", "mix": "Mix", "bypass": "Bypass"}, "responseFormat": "Response Format", "responseFormatTooltip": "Defines the response format. Examples:\n• Multiple Paragraphs\n• Single Paragraph\n• Bullet Points", "responseFormatOptions": {"multipleParagraphs": "Multiple Paragraphs", "singleParagraph": "Single Paragraph", "bulletPoints": "Bullet Points"}, "topK": "Top K Results", "topKTooltip": "Number of top items to retrieve. Represents entities in 'local' mode and relationships in 'global' mode", "topKPlaceholder": "Number of results", "maxTokensTextUnit": "<PERSON> for Text Unit", "maxTokensTextUnitTooltip": "Maximum number of tokens allowed for each retrieved text chunk", "maxTokensGlobalContext": "<PERSON> for Global Context", "maxTokensGlobalContextTooltip": "Maximum number of tokens allocated for relationship descriptions in global retrieval", "maxTokensLocalContext": "<PERSON> for Local Context", "maxTokensLocalContextTooltip": "Maximum number of tokens allocated for entity descriptions in local retrieval", "historyTurns": "History Turns", "historyTurnsTooltip": "Number of complete conversation turns (user-assistant pairs) to consider in the response context", "historyTurnsPlaceholder": "Number of history turns", "onlyNeedContext": "Only Need Context", "onlyNeedContextTooltip": "If True, only returns the retrieved context without generating a response", "onlyNeedPrompt": "Only Need Prompt", "onlyNeedPromptTooltip": "If True, only returns the generated prompt without producing a response", "streamResponse": "Stream Response", "streamResponseTooltip": "If True, enables streaming output for real-time responses", "userPrompt": "User Prompt", "userPromptTooltip": "Provide additional response requirements to the LLM (unrelated to query content, only for output processing).", "userPromptPlaceholder": "Enter custom prompt (optional)"}}, "apiSite": {"loading": "Loading API Documentation..."}, "apiKeyAlert": {"title": "API Key is required", "description": "Please enter your API key to access the service", "placeholder": "Enter your API key", "save": "Save"}}