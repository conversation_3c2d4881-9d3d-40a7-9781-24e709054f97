{"settings": {"language": "<PERSON><PERSON>", "theme": "Thème", "light": "<PERSON>", "dark": "Sombre", "system": "Système"}, "header": {"documents": "Documents", "knowledgeGraph": "Graphe de connaissances", "retrieval": "Récupération", "api": "API", "projectRepository": "Référentiel du projet", "logout": "Déconnexion", "themeToggle": {"switchToLight": "Passer au thème clair", "switchToDark": "Passer au thème sombre"}}, "login": {"description": "Veuillez entrer votre compte et mot de passe pour vous connecter au système", "username": "Nom d'utilisateur", "usernamePlaceholder": "Veuillez saisir un nom d'utilisateur", "password": "Mot de passe", "passwordPlaceholder": "Veuillez saisir un mot de passe", "loginButton": "Connexion", "loggingIn": "Connexion en cours...", "successMessage": "Connexion réussie", "errorEmptyFields": "Veuillez saisir votre nom d'utilisateur et mot de passe", "errorInvalidCredentials": "Échec de la connexion, veuillez vérifier le nom d'utilisateur et le mot de passe", "authDisabled": "L'authentification est désactivée. Utilisation du mode sans connexion.", "guestMode": "Mode sans connexion"}, "common": {"cancel": "Annuler", "save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Sauvegarde en cours...", "saveFailed": "Échec de la sauvegarde"}, "documentPanel": {"clearDocuments": {"button": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Effacer les documents", "title": "Effacer les documents", "description": "Cette action supprimera tous les documents du système", "warning": "ATTENTION : Cette action supprimera définitivement tous les documents et ne peut pas être annulée !", "confirm": "Voulez-vous vraiment effacer tous les documents ?", "confirmPrompt": "Tapez 'yes' pour confirmer cette action", "confirmPlaceholder": "Tapez yes pour confirmer", "clearCache": "Effacer le cache LLM", "confirmButton": "OUI", "success": "Documents effacés avec succès", "cacheCleared": "<PERSON><PERSON> effac<PERSON> avec succès", "cacheClearFailed": "Échec de l'effacement du cache :\n{{error}}", "failed": "Échec de l'effacement des documents :\n{{message}}", "error": "Échec de l'effacement des documents :\n{{error}}"}, "deleteDocuments": {"button": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Supprimer les documents sélectionnés", "title": "Supprimer les documents", "description": "Cette action supprimera définitivement les documents sélectionnés du système", "warning": "ATTENTION : Cette action supprimera définitivement les documents sélectionnés et ne peut pas être annulée !", "confirm": "Voulez-vous vraiment supprimer {{count}} document(s) sélectionné(s) ?", "confirmPrompt": "Tapez 'yes' pour confirmer cette action", "confirmPlaceholder": "Tapez yes pour confirmer", "confirmButton": "OUI", "deleteFileOption": "Supprimer également les fichiers téléchargés", "deleteFileTooltip": "Cochez cette option pour supprimer également les fichiers téléchargés correspondants sur le serveur", "success": "Pipeline de suppression de documents démarré avec succès", "failed": "Échec de la suppression des documents :\n{{message}}", "error": "Échec de la suppression des documents :\n{{error}}", "busy": "Le pipeline est occupé, veuillez réessayer plus tard", "notAllowed": "Aucune autorisation pour effectuer cette opération", "cannotDeleteAll": "Impossible de supprimer tous les documents. Si vous devez supprimer tous les documents, veuillez utiliser la fonction Effacer les documents."}, "deselectDocuments": {"button": "Désé<PERSON><PERSON>ner", "tooltip": "Désélectionner tous les documents sélectionnés", "title": "Désélectionner les documents", "description": "Cette action effacera tous les documents sélectionnés ({{count}} sélectionnés)", "confirmButton": "<PERSON><PERSON>"}, "uploadDocuments": {"button": "Télécharger", "tooltip": "Télécharger des documents", "title": "Télécharger des documents", "description": "Glissez-<PERSON><PERSON><PERSON>z vos documents ici ou cliquez pour parcourir.", "single": {"uploading": "Téléchargement de {{name}} : {{percent}}%", "success": "Succès du téléchargement :\n{{name}} téléchargé avec succès", "failed": "Échec du téléchargement :\n{{name}}\n{{message}}", "error": "Échec du téléchargement :\n{{name}}\n{{error}}"}, "batch": {"uploading": "Téléchargement des fichiers...", "success": "Fichiers téléchargés avec succès", "error": "Certains fichiers n'ont pas pu être téléchargés"}, "generalError": "Échec du téléchargement\n{{error}}", "fileTypes": "Types pris en charge : TXT, MD, DOCX, PDF, PPTX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS", "fileUploader": {"singleFileLimit": "Impossible de télécharger plus d'un fichier à la fois", "maxFilesLimit": "Impossible de télécharger plus de {{count}} fichiers", "fileRejected": "Le fichier {{name}} a été rejeté", "unsupportedType": "Type de fichier non pris en charge", "fileTooLarge": "Fichier trop volumineux, taille maximale {{maxSize}}", "dropHere": "Déposez les fichiers ici", "dragAndDrop": "G<PERSON><PERSON><PERSON> et déposez les fichiers ici, ou cliquez pour sélectionner", "removeFile": "<PERSON><PERSON><PERSON><PERSON> le fichier", "uploadDescription": "Vous pouvez télécharger {{isMultiple ? 'plusieurs' : count}} fichiers (jusqu'à {{maxSize}} chacun)", "duplicateFile": "Le nom du fichier existe déjà dans le cache du serveur"}}, "documentManager": {"title": "Gestion des documents", "scanButton": "Scanner", "scanTooltip": "Scanner les documents dans le dossier d'entrée", "pipelineStatusButton": "État du Pipeline", "pipelineStatusTooltip": "Voir l'état du pipeline", "uploadedTitle": "Documents téléchargés", "uploadedDescription": "Liste des documents téléchargés et leurs statuts.", "emptyTitle": "Aucun document", "emptyDescription": "Il n'y a pas encore de documents téléchargés.", "columns": {"id": "ID", "summary": "Résumé", "status": "Statut", "length": "<PERSON><PERSON><PERSON>", "chunks": "Fragments", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "metadata": "Métadonnées", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"all": "Tous", "completed": "<PERSON><PERSON><PERSON><PERSON>", "processing": "En traitement", "pending": "En attente", "failed": "<PERSON><PERSON><PERSON>"}, "errors": {"loadFailed": "Échec du chargement des documents\n{{error}}", "scanFailed": "Échec de la numérisation des documents\n{{error}}", "scanProgressFailed": "Échec de l'obtention de la progression de la numérisation\n{{error}}"}, "fileNameLabel": "Nom du fichier", "showButton": "<PERSON><PERSON><PERSON><PERSON>", "hideButton": "Masquer", "showFileNameTooltip": "<PERSON><PERSON><PERSON><PERSON> le nom du fichier", "hideFileNameTooltip": "Masquer le nom du fichier"}, "pipelineStatus": {"title": "État du Pipeline", "busy": "Pipeline occupé", "requestPending": "Requête en attente", "jobName": "Nom du travail", "startTime": "<PERSON><PERSON> d<PERSON>", "progress": "Progression", "unit": "lot", "latestMessage": "<PERSON><PERSON> message", "historyMessages": "Historique des messages", "errors": {"fetchFailed": "Échec de la récupération de l'état du pipeline\n{{error}}"}}}, "graphPanel": {"dataIsTruncated": "Les données du graphe sont tronquées au nombre maximum de nœuds", "statusDialog": {"title": "Paramètres du Serveur LightRAG", "description": "Afficher l'état actuel du système et les informations de connexion"}, "legend": "Légende", "nodeTypes": {"person": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON><PERSON>", "geo": "Géographique", "location": "Emplacement", "organization": "Organisation", "event": "Événement", "equipment": "Équipement", "weapon": "Arme", "animal": "Animal", "unknown": "Inconnu", "object": "Objet", "group": "Groupe", "technology": "Technologie"}, "sideBar": {"settings": {"settings": "Paramètres", "healthCheck": "Vérification de l'état", "showPropertyPanel": "Afficher le panneau des propriétés", "showSearchBar": "Afficher la barre de recherche", "showNodeLabel": "Afficher l'étiquette du nœud", "nodeDraggable": "<PERSON><PERSON><PERSON> déplaçable", "showEdgeLabel": "Afficher l'étiquette de l'arête", "hideUnselectedEdges": "Masquer les arêtes non sélectionnées", "edgeEvents": "Événements des arêtes", "maxQueryDepth": "Profondeur maximale de la requête", "maxNodes": "Nombre maximum de nœuds", "maxLayoutIterations": "Itérations maximales de mise en page", "resetToDefault": "Réinitialiser par défaut", "edgeSizeRange": "Plage de taille des arêtes", "depth": "D", "max": "Max", "degree": "<PERSON><PERSON><PERSON>", "apiKey": "Clé API", "enterYourAPIkey": "Entrez votre clé API", "save": "<PERSON><PERSON><PERSON><PERSON>", "refreshLayout": "Actualiser la mise en page"}, "zoomControl": {"zoomIn": "Zoom avant", "zoomOut": "Zoom arri<PERSON>", "resetZoom": "Réinitialiser le zoom", "rotateCamera": "Rotation horaire", "rotateCameraCounterClockwise": "Rotation antihoraire"}, "layoutsControl": {"startAnimation": "Démarrer l'animation de mise en page", "stopAnimation": "Arr<PERSON><PERSON> l'animation de mise en page", "layoutGraph": "Mettre en page le graphe", "layouts": {"Circular": "Circulaire", "Circlepack": "Paquet circulaire", "Random": "Aléatoire", "Noverlaps": "Sans chevauchement", "Force Directed": "Di<PERSON><PERSON> par la force", "Force Atlas": "Atlas de force"}}, "fullScreenControl": {"fullScreen": "Plein écran", "windowed": "<PERSON><PERSON><PERSON><PERSON>"}, "legendControl": {"toggleLegend": "Basculer la légende"}}, "statusIndicator": {"connected": "Connecté", "disconnected": "Déconnecté"}, "statusCard": {"unavailable": "Informations sur l'état indisponibles", "storageInfo": "Informations de stockage", "workingDirectory": "Répertoire de travail", "inputDirectory": "Répertoire d'entrée", "llmConfig": "Configuration du modèle de langage", "llmBinding": "Liaison du modèle de langage", "llmBindingHost": "Hôte de liaison du modèle de langage", "llmModel": "<PERSON><PERSON><PERSON><PERSON>", "maxTokens": "Nombre maximum de jetons", "embeddingConfig": "Configuration d'incorporation", "embeddingBinding": "Liaison d'incorporation", "embeddingBindingHost": "Hôte de liaison d'incorporation", "embeddingModel": "Modèle d'incorporation", "storageConfig": "Configuration de stockage", "kvStorage": "Stockage clé-valeur", "docStatusStorage": "Stockage de l'état des documents", "graphStorage": "Stockage du graphe", "vectorStorage": "Stockage vectoriel"}, "propertiesView": {"editProperty": "Modifier {{property}}", "editPropertyDescription": "Modifiez la valeur de la propriété dans la zone de texte ci-dessous.", "errors": {"duplicateName": "Le nom du nœud existe déjà", "updateFailed": "Échec de la mise à jour du nœud", "tryAgainLater": "Veuillez réessayer plus tard"}, "success": {"entityUpdated": "<PERSON><PERSON>ud mis à jour avec succès", "relationUpdated": "Relation mise à jour avec succès"}, "node": {"title": "<PERSON><PERSON><PERSON>", "id": "ID", "labels": "Étiquettes", "degree": "<PERSON><PERSON><PERSON>", "properties": "Propriétés", "relationships": "Relations(dans le sous-graphe)", "expandNode": "Développer le nœud", "pruneNode": "<PERSON><PERSON><PERSON> le nœud", "deleteAllNodesError": "<PERSON><PERSON><PERSON> de supprimer tous les nœuds du graphe", "nodesRemoved": "{{count}} nœuds supprimés, y compris les nœuds orphelins", "noNewNodes": "Aucun nœud développable trouvé", "propertyNames": {"description": "Description", "entity_id": "Nom", "entity_type": "Type", "source_id": "ID source", "Neighbour": "Voisin", "file_path": "Source", "keywords": "Keys", "weight": "Poids"}}, "edge": {"title": "Relation", "id": "ID", "type": "Type", "source": "Source", "target": "Cible", "properties": "Propriétés"}}, "search": {"placeholder": "Rechercher des nœuds...", "message": "Et {{count}} autres"}, "graphLabels": {"selectTooltip": "Sélectionner l'étiquette de la requête", "noLabels": "<PERSON><PERSON>ne éti<PERSON>te trouvée", "label": "Étiquette", "placeholder": "Rechercher des étiquettes...", "andOthers": "Et {{count}} autres", "refreshTooltip": "Recharger les données (<PERSON><PERSON> l'ajout de fi<PERSON>)"}, "emptyGraph": "Vide (Essayez de recharger)"}, "retrievePanel": {"chatMessage": {"copyTooltip": "Copier dans le presse-papiers", "copyError": "Échec de la copie du texte dans le presse-papiers"}, "retrieval": {"startPrompt": "<PERSON><PERSON><PERSON>rez une récupération en tapant votre requête ci-dessous", "clear": "<PERSON><PERSON><PERSON><PERSON>", "send": "Envoyer", "placeholder": "Tapez votre requête (Préfixe de requête : /<Query Mode>)", "error": "Erreur : Échec de l'obtention de la réponse", "queryModeError": "Seuls les modes de requête suivants sont pris en charge : {{modes}}", "queryModePrefixInvalid": "Préfixe de mode de requête invalide. Utilisez : /<mode> [espace] votre requête"}, "querySettings": {"parametersTitle": "Paramètres", "parametersDescription": "Configurez vos paramètres de requête", "queryMode": "Mode de requête", "queryModeTooltip": "Sélectionnez la stratégie de récupération :\n• Naïf : Recherche de base sans techniques avancées\n• Local : Récupération d'informations dépendante du contexte\n• Global : Utilise une base de connaissances globale\n• Hybride : Combine récupération locale et globale\n• Mixte : Intègre le graphe de connaissances avec la récupération vectorielle\n• Bypass : Transmet directement la requête au LLM sans récupération", "queryModeOptions": {"naive": "<PERSON><PERSON><PERSON>", "local": "Local", "global": "Global", "hybrid": "<PERSON>e", "mix": "Mixte", "bypass": "Bypass"}, "responseFormat": "Format de réponse", "responseFormatTooltip": "Définit le format de la réponse. Exemples :\n• Plusieurs paragraphes\n• Paragraphe unique\n• Points à puces", "responseFormatOptions": {"multipleParagraphs": "Plusieurs paragraphes", "singleParagraph": "Paragraphe unique", "bulletPoints": "Points à puces"}, "topK": "Top K résultats", "topKTooltip": "Nombre d'éléments supérieurs à récupérer. Représente les entités en mode 'local' et les relations en mode 'global'", "topKPlaceholder": "Nombre de résultats", "maxTokensTextUnit": "Nombre maximum de jetons pour l'unité de texte", "maxTokensTextUnitTooltip": "Nombre maximum de jetons autorisés pour chaque fragment de texte récupéré", "maxTokensGlobalContext": "Nombre maximum de jetons pour le contexte global", "maxTokensGlobalContextTooltip": "Nombre maximum de jetons alloués pour les descriptions des relations dans la récupération globale", "maxTokensLocalContext": "Nombre maximum de jetons pour le contexte local", "maxTokensLocalContextTooltip": "Nombre maximum de jetons alloués pour les descriptions des entités dans la récupération locale", "historyTurns": "Tours d'historique", "historyTurnsTooltip": "Nombre de tours complets de conversation (paires utilisateur-assistant) à prendre en compte dans le contexte de la réponse", "historyTurnsPlaceholder": "Nombre de tours d'historique", "onlyNeedContext": "Besoin uniquement du contexte", "onlyNeedContextTooltip": "Si vrai, ne renvoie que le contexte récupéré sans générer de réponse", "onlyNeedPrompt": "Besoin uniquement de l'invite", "onlyNeedPromptTooltip": "Si vrai, ne renvoie que l'invite générée sans produire de réponse", "streamResponse": "Réponse en flux", "streamResponseTooltip": "<PERSON> vrai, active la sortie en flux pour des réponses en temps réel", "userPrompt": "<PERSON><PERSON><PERSON>", "userPromptTooltip": "Fournir des exigences de réponse supplémentaires au LLM (sans rapport avec le contenu de la requête, uniquement pour le traitement de sortie).", "userPromptPlaceholder": "Entrez une invite personnalisée (facultatif)"}}, "apiSite": {"loading": "Chargement de la documentation de l'API..."}, "apiKeyAlert": {"title": "Clé API requise", "description": "Veuillez entrer votre clé API pour accéder au service", "placeholder": "Entrez votre clé API", "save": "<PERSON><PERSON><PERSON><PERSON>"}}