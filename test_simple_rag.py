#!/usr/bin/env python3
"""
Test del agente RAG simplificado con modelos Hugging Face
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


async def test_configuration():
    """Prueba la configuración"""
    print("🔧 Probando configuración...")
    
    try:
        from agent.config import LLM_CONFIG, EMBEDDING_CONFIG
        
        print(f"✅ LLM Provider: {LLM_CONFIG['provider']}")
        print(f"✅ LLM Model: {LLM_CONFIG['model']}")
        print(f"✅ Embedding Provider: {EMBEDDING_CONFIG['provider']}")
        print(f"✅ Embedding Model: {EMBEDDING_CONFIG['model']}")
        
        # Verificar que transformers sea el proveedor
        assert LLM_CONFIG['provider'] == 'transformers', f"Expected transformers, got {LLM_CONFIG['provider']}"
        print("✅ Hugging Face transformers configurado correctamente")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        return False


async def test_embedder():
    """Prueba el embedder"""
    print("\n🔤 Probando embedder...")
    
    try:
        from agent.simple_rag_agent import HuggingFaceEmbedder
        
        embedder = HuggingFaceEmbedder()
        
        # Probar embedding
        test_text = "¿Qué es la inteligencia artificial?"
        embedding = await embedder.acall(test_text)
        
        print(f"✅ Embedding generado: dimensión {len(embedding)}")
        print(f"✅ Primeros valores: {embedding[:5]}")
        
        # Verificar dimensión
        from agent.config import EMBEDDING_CONFIG
        expected_dim = EMBEDDING_CONFIG["dimensions"]
        assert len(embedding) == expected_dim, f"Expected {expected_dim}, got {len(embedding)}"
        
        return embedder
        
    except Exception as e:
        print(f"❌ Error en embedder: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_generator():
    """Prueba el generador"""
    print("\n🤖 Probando generador...")
    
    try:
        from agent.simple_rag_agent import HuggingFaceGenerator
        
        generator = HuggingFaceGenerator()
        
        # Probar generación
        test_prompt = "¿Qué es la inteligencia artificial?"
        output = await generator.acall(test_prompt)
        
        response = output.data if hasattr(output, 'data') else str(output)
        print(f"✅ Respuesta generada: {response[:100]}...")
        
        # Verificar que no esté vacía
        assert len(response.strip()) > 0, "Respuesta vacía"
        
        return generator
        
    except Exception as e:
        print(f"❌ Error en generador: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_retriever(embedder):
    """Prueba el retriever"""
    print("\n🔍 Probando retriever...")
    
    try:
        from agent.simple_rag_agent import SimpleVectorRetriever
        from lightrag.core import Document, UserQuery
        
        retriever = SimpleVectorRetriever(embedder)
        
        # Crear documentos de prueba
        test_docs = [
            Document(
                text="LightRAG es un framework para Retrieval-Augmented Generation que combina búsqueda vectorial y de grafos.",
                meta_data={"source": "test1"}
            ),
            Document(
                text="Los modelos de Hugging Face permiten ejecutar IA localmente sin APIs pagadas.",
                meta_data={"source": "test2"}
            ),
            Document(
                text="La búsqueda semántica utiliza embeddings para encontrar contenido relevante por significado.",
                meta_data={"source": "test3"}
            )
        ]
        
        # Agregar documentos
        await retriever.add_documents(test_docs)
        print(f"✅ Agregados {len(test_docs)} documentos")
        
        # Probar búsqueda
        query = UserQuery(query="¿Qué es LightRAG?")
        result = await retriever.acall(query)
        
        print(f"✅ Encontrados {len(result.documents)} documentos relevantes")
        
        if result.documents:
            for i, doc in enumerate(result.documents):
                score = getattr(doc, 'score', 'N/A')
                print(f"   Doc {i+1}: score={score}, texto={doc.text[:50]}...")
        
        return retriever
        
    except Exception as e:
        print(f"❌ Error en retriever: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_simple_agent():
    """Prueba el agente completo"""
    print("\n🚀 Probando agente RAG completo...")
    
    try:
        from agent.simple_rag_agent import SimpleRAGAgent
        
        # Crear agente
        agent = SimpleRAGAgent()
        print("✅ Agente creado")
        
        # Crear documentos de prueba si no existen
        docs_dir = Path("docs")
        if not docs_dir.exists():
            docs_dir.mkdir(exist_ok=True)
            
            # Crear documento de prueba
            test_doc = docs_dir / "test_lightrag.md"
            test_doc.write_text("""
# LightRAG Framework

## ¿Qué es LightRAG?
LightRAG es un framework avanzado para Retrieval-Augmented Generation que combina:
- Búsqueda vectorial usando embeddings
- Grafo de conocimiento para relaciones
- Múltiples modos de consulta

## Modelos Hugging Face
Los modelos de Hugging Face permiten:
- Ejecutar IA localmente
- No depender de APIs pagadas
- Tener control total sobre los datos

## Arquitectura
El sistema utiliza:
1. Sentence-transformers para embeddings
2. Transformers para generación de texto
3. Búsqueda vectorial para recuperación
""")
            print("✅ Documento de prueba creado")
        
        # Cargar documentos
        success = await agent.load_documents()
        print(f"✅ Documentos cargados: {success}")
        
        # Obtener estadísticas
        stats = await agent.get_stats()
        print(f"✅ Estadísticas: {stats}")
        
        # Probar consultas
        test_queries = [
            "¿Qué es LightRAG?",
            "¿Cómo funcionan los modelos Hugging Face?",
            "¿Cuál es la arquitectura del sistema?"
        ]
        
        for query in test_queries:
            print(f"\n📝 Consulta: {query}")
            response = await agent.query(query)
            print(f"✅ Respuesta: {response[:150]}...")
        
        return agent
        
    except Exception as e:
        print(f"❌ Error en agente: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_adk_tools():
    """Prueba las herramientas ADK adaptadas"""
    print("\n🔗 Probando herramientas ADK...")
    
    try:
        # Crear una versión simplificada de las herramientas ADK
        from agent.simple_rag_agent import get_simple_agent
        
        agent = get_simple_agent()
        
        # Simular herramientas ADK
        class SimpleLightRAGTool:
            def __init__(self):
                self.agent = agent
            
            async def execute(self, question: str, mode: str = "vector"):
                try:
                    response = await self.agent.query(question, mode)
                    return {
                        "success": True,
                        "response": response,
                        "mode": mode,
                        "question": question
                    }
                except Exception as e:
                    return {
                        "success": False,
                        "error": str(e),
                        "question": question
                    }
        
        # Probar herramienta
        tool = SimpleLightRAGTool()
        result = await tool.execute("¿Qué es LightRAG?")
        
        print(f"✅ Herramienta ADK: {result['success']}")
        if result['success']:
            print(f"✅ Respuesta: {result['response'][:100]}...")
        else:
            print(f"❌ Error: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en herramientas ADK: {e}")
        return False


async def main():
    """Función principal"""
    print("🚀 Iniciando pruebas del agente RAG simplificado con Hugging Face\n")
    
    results = {}
    
    # 1. Configuración
    results['config'] = await test_configuration()
    
    if not results['config']:
        print("❌ Configuración falló, abortando")
        return
    
    # 2. Embedder
    embedder = await test_embedder()
    results['embedder'] = embedder is not None
    
    # 3. Generador
    generator = await test_generator()
    results['generator'] = generator is not None
    
    # 4. Retriever
    if embedder:
        retriever = await test_retriever(embedder)
        results['retriever'] = retriever is not None
    else:
        results['retriever'] = False
    
    # 5. Agente completo
    agent = await test_simple_agent()
    results['agent'] = agent is not None
    
    # 6. Herramientas ADK
    results['adk_tools'] = await test_adk_tools()
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests >= 4:
        print("🎉 ¡Agente RAG funcionando con Hugging Face!")
        print("\n💡 Funcionalidades verificadas:")
        print("- ✅ Embeddings locales con sentence-transformers")
        print("- ✅ Generación de texto con transformers")
        print("- ✅ Búsqueda vectorial semántica")
        print("- ✅ Pipeline RAG completo")
        print("- ✅ Integración con herramientas ADK")
        
        print("\n🚀 Comandos para usar el agente:")
        print("python -c \"from agent.simple_rag_agent import get_simple_agent; import asyncio; agent = get_simple_agent(); print(asyncio.run(agent.query('¿Qué es LightRAG?')))\"")
        
    else:
        print("⚠️  Algunas funcionalidades necesitan ajustes")


if __name__ == "__main__":
    asyncio.run(main())
