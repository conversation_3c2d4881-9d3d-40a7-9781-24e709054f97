#!/usr/bin/env python3
"""
Test completo del agente LightRAG con AWS Bedrock
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


async def test_bedrock_configuration():
    """Prueba la configuración de Bedrock"""
    print("🔧 Probando configuración de Bedrock...")
    
    try:
        from agent.config.lightrag_config import (
            AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG
        )
        
        print(f"✅ Modelos disponibles: {AVAILABLE_MODELS}")
        print(f"✅ LLM Provider: {LLM_CONFIG['provider']}")
        print(f"✅ LLM Model: {LLM_CONFIG['model']}")
        print(f"✅ Embedding Provider: {EMBEDDING_CONFIG['provider']}")
        print(f"✅ Embedding Model: {EMBEDDING_CONFIG['model']}")
        
        # Verificar que Bedrock sea el proveedor si está disponible
        if AVAILABLE_MODELS.get("bedrock", False):
            assert LLM_CONFIG['provider'] == 'bedrock', f"Expected bedrock, got {LLM_CONFIG['provider']}"
            assert EMBEDDING_CONFIG['provider'] == 'bedrock', f"Expected bedrock, got {EMBEDDING_CONFIG['provider']}"
            print("✅ Bedrock configurado como proveedor primario")
            return True
        else:
            print("⚠️  Bedrock no disponible, usando fallback")
            return False
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bedrock_agent():
    """Prueba el agente Bedrock"""
    print("\n🤖 Probando agente Bedrock...")
    
    try:
        from agent.bedrock_rag_agent import BedrockRAGAgent
        
        # Crear agente
        agent = BedrockRAGAgent()
        print("✅ Agente Bedrock creado")
        
        # Probar generación de embedding
        print("🔤 Probando embeddings...")
        embedding = await agent.generate_embedding("Texto de prueba")
        print(f"✅ Embedding generado: {len(embedding)} dimensiones")
        
        # Probar generación de respuesta
        print("💬 Probando generación de respuesta...")
        response = await agent.generate_response("¿Qué es AWS Bedrock?")
        print(f"✅ Respuesta: {response[:100]}...")
        
        return agent
        
    except Exception as e:
        print(f"❌ Error en agente Bedrock: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_document_loading(agent):
    """Prueba la carga de documentos"""
    print("\n📚 Probando carga de documentos...")
    
    try:
        # Verificar directorio de documentos
        docs_dir = Path("docs")
        if not docs_dir.exists():
            print("📁 Creando documentos de prueba...")
            docs_dir.mkdir(exist_ok=True)
            
            # Crear documento específico para Bedrock
            bedrock_doc = docs_dir / "bedrock_info.md"
            bedrock_doc.write_text("""
# AWS Bedrock

## ¿Qué es AWS Bedrock?
AWS Bedrock es un servicio completamente administrado que proporciona acceso a modelos de IA generativa de alto rendimiento a través de una API.

## Modelos Disponibles
- **Anthropic Claude**: Modelos conversacionales avanzados
- **Amazon Titan**: Modelos de texto y embeddings
- **Meta Llama**: Modelos de código abierto
- **Cohere**: Modelos especializados en embeddings

## Características Principales
- Acceso a múltiples modelos de IA
- Escalabilidad automática
- Seguridad y privacidad
- Integración con otros servicios AWS

## Casos de Uso
- Chatbots y asistentes virtuales
- Generación de contenido
- Análisis de documentos
- Búsqueda semántica
""")
        
        # Cargar documentos
        success = await agent.load_documents()
        print(f"✅ Documentos cargados: {success}")
        
        # Obtener estadísticas
        stats = await agent.get_stats()
        print(f"✅ Estadísticas: {stats}")
        
        return success
        
    except Exception as e:
        print(f"❌ Error cargando documentos: {e}")
        return False


async def test_rag_queries(agent):
    """Prueba consultas RAG"""
    print("\n🔍 Probando consultas RAG...")
    
    try:
        # Consultas de prueba específicas para Bedrock
        test_queries = [
            "¿Qué es AWS Bedrock?",
            "¿Qué modelos están disponibles en Bedrock?",
            "¿Cuáles son las características principales de Bedrock?",
            "¿Para qué casos de uso se puede utilizar Bedrock?"
        ]
        
        for query in test_queries:
            print(f"\n📝 Consulta: {query}")
            response = await agent.query(query, mode="hybrid")
            print(f"✅ Respuesta: {response[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en consultas RAG: {e}")
        return False


async def test_bedrock_adk_tools():
    """Prueba las herramientas ADK de Bedrock"""
    print("\n🛠️  Probando herramientas ADK de Bedrock...")
    
    try:
        from agent.bedrock_adk_integration import (
            BedrockLightRAGTool, BedrockDocumentLoaderTool, 
            BedrockKnowledgeGraphTool, BedrockModelTestTool
        )
        
        # Probar BedrockLightRAGTool
        print("📝 Probando BedrockLightRAGTool...")
        tool = BedrockLightRAGTool()
        result = await tool.execute("¿Qué modelos ofrece AWS Bedrock?")
        print(f"✅ LightRAGTool: {result['success']}")
        if result['success']:
            print(f"   Respuesta: {result['response'][:80]}...")
        
        # Probar BedrockDocumentLoaderTool
        print("📚 Probando BedrockDocumentLoaderTool...")
        tool = BedrockDocumentLoaderTool()
        result = await tool.execute()
        print(f"✅ DocumentLoaderTool: {result['success']}")
        print(f"   Mensaje: {result['message']}")
        
        # Probar BedrockKnowledgeGraphTool
        print("📊 Probando BedrockKnowledgeGraphTool...")
        tool = BedrockKnowledgeGraphTool()
        result = await tool.execute()
        print(f"✅ KnowledgeGraphTool: {result['success']}")
        if result['success']:
            print(f"   Stats: {result['stats']}")
        
        # Probar BedrockModelTestTool
        print("🧪 Probando BedrockModelTestTool...")
        tool = BedrockModelTestTool()
        result = await tool.execute("both")
        print(f"✅ ModelTestTool: {result['success']}")
        if result['success']:
            print(f"   Resultados: {result['results']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en herramientas ADK: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bedrock_adk_agent():
    """Prueba el agente ADK completo"""
    print("\n🤖 Probando agente ADK Bedrock completo...")
    
    try:
        from agent.bedrock_adk_integration import BedrockADKAgent
        
        # Crear agente ADK
        adk_agent = BedrockADKAgent()
        print("✅ Agente ADK Bedrock creado")
        
        # Inicializar documentos
        success = await adk_agent.initialize_documents()
        print(f"✅ Documentos inicializados: {success}")
        
        # Probar diferentes tipos de mensajes
        test_messages = [
            "probar modelos",
            "¿Qué es AWS Bedrock y qué ventajas ofrece?",
            "estadísticas del sistema",
            "cargar documentos",
            "¿Cómo se compara Bedrock con otros servicios de IA?"
        ]
        
        for message in test_messages:
            print(f"\n📝 Mensaje: {message}")
            result = await adk_agent.process_message(message)
            
            if result['success']:
                print(f"✅ Respuesta exitosa")
                if 'response' in result:
                    print(f"   Contenido: {result['response'][:80]}...")
                elif 'message' in result:
                    print(f"   Mensaje: {result['message']}")
                elif 'stats' in result:
                    print(f"   Stats: {result['stats']}")
                elif 'results' in result:
                    print(f"   Resultados: {result['results']}")
            else:
                print(f"❌ Error: {result['error']}")
        
        # Obtener herramientas disponibles
        tools = await adk_agent.get_available_tools()
        print(f"\n✅ Herramientas disponibles: {list(tools.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en agente ADK: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_search_modes(agent):
    """Prueba diferentes modos de búsqueda"""
    print("\n🔀 Probando modos de búsqueda...")
    
    try:
        query = "¿Cuáles son los casos de uso principales de AWS Bedrock?"
        modes = ["naive", "local", "global", "hybrid"]
        
        for mode in modes:
            print(f"\n🔍 Modo {mode}:")
            response = await agent.query(query, mode)
            print(f"✅ Respuesta ({mode}): {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en modos de búsqueda: {e}")
        return False


async def main():
    """Función principal"""
    print("🚀 Test completo de LightRAG con AWS Bedrock\n")
    
    results = {}
    
    # 1. Configuración
    results['configuration'] = await test_bedrock_configuration()
    
    if not results['configuration']:
        print("\n⚠️  Bedrock no disponible, ejecutando pruebas limitadas")
        print("\n🔧 Para configurar Bedrock:")
        print("1. aws configure --profile IA")
        print("2. Configurar región: us-east-2")
        print("3. Verificar permisos de Bedrock")
        print("4. Habilitar modelos en la consola de Bedrock")
        return
    
    # 2. Agente Bedrock
    agent = await test_bedrock_agent()
    results['agent'] = agent is not None
    
    if not agent:
        print("❌ Agente Bedrock falló, abortando pruebas")
        return
    
    # 3. Carga de documentos
    results['document_loading'] = await test_document_loading(agent)
    
    # 4. Consultas RAG
    results['rag_queries'] = await test_rag_queries(agent)
    
    # 5. Modos de búsqueda
    results['search_modes'] = await test_search_modes(agent)
    
    # 6. Herramientas ADK
    results['adk_tools'] = await test_bedrock_adk_tools()
    
    # 7. Agente ADK completo
    results['adk_agent'] = await test_bedrock_adk_agent()
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests >= 5:
        print("\n🎉 ¡LightRAG con AWS Bedrock funcionando perfectamente!")
        print("\n✅ Funcionalidades verificadas:")
        print("- Configuración con Bedrock como proveedor primario")
        print("- Generación de embeddings con Titan Embeddings")
        print("- Generación de texto con Claude 3 Haiku")
        print("- Carga y procesamiento de documentos")
        print("- Búsqueda vectorial semántica")
        print("- Pipeline RAG completo")
        print("- Múltiples modos de búsqueda")
        print("- Integración completa con Google ADK")
        print("- Herramientas ADK especializadas")
        
        print("\n🚀 Comandos para usar:")
        print("python test_bedrock_complete.py")
        
        print("\n💡 Configuración verificada:")
        print("- Región: us-east-2")
        print("- Perfil AWS: IA")
        print("- LLM: anthropic.claude-3-haiku-20240307-v1:0")
        print("- Embeddings: amazon.titan-embed-text-v1")
        
    else:
        print("⚠️  Algunas funcionalidades necesitan ajustes")


if __name__ == "__main__":
    asyncio.run(main())
