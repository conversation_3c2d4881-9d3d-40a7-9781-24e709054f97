#!/usr/bin/env python3
"""
Test específico para Amazon Nova Micro en AWS Bedrock
"""
import asyncio
import os
import sys
import json
from pathlib import Path

# Agregar el directorio del proyecto al path
sys.path.insert(0, str(Path(__file__).parent))


async def test_nova_micro_direct():
    """Prueba directa de Amazon Nova Micro"""
    print("🚀 Probando Amazon Nova Micro directamente...")
    
    try:
        import boto3
        from botocore.exceptions import NoCredentialsError, ClientError
        
        # Crear cliente Bedrock
        session = boto3.Session(profile_name="IA")
        bedrock_client = session.client('bedrock-runtime', region_name='us-east-2')
        
        # Configurar modelo Nova Micro
        model_id = "amazon.nova-micro-v1:0"
        
        # Mensaje de prueba
        messages = [
            {
                "role": "user",
                "content": [{"text": "¿Qué es AWS Bedrock? Responde en español en máximo 100 palabras."}]
            }
        ]
        
        body = {
            "messages": messages,
            "inferenceConfig": {
                "max_new_tokens": 200,
                "temperature": 0.1,
                "top_p": 0.9
            }
        }
        
        print(f"📝 Modelo: {model_id}")
        print(f"📝 Mensaje: {messages[0]['content'][0]['text']}")
        
        # Llamar a Nova Micro
        response = bedrock_client.invoke_model(
            modelId=model_id,
            body=json.dumps(body),
            contentType="application/json",
            accept="application/json"
        )
        
        # Procesar respuesta
        response_body = json.loads(response['body'].read())
        
        completion = ""
        if "output" in response_body and "message" in response_body["output"]:
            content = response_body["output"]["message"].get("content", [])
            if content and len(content) > 0:
                completion = content[0].get("text", "")
        
        print(f"✅ Respuesta Nova Micro: {completion}")
        
        # Verificar que la respuesta no esté vacía
        assert len(completion.strip()) > 0, "Respuesta vacía"
        
        return True
        
    except Exception as e:
        print(f"❌ Error con Nova Micro: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_nova_configuration():
    """Prueba la configuración del agente con Nova"""
    print("\n⚙️  Probando configuración con Nova...")
    
    try:
        from agent.config.lightrag_config import (
            AVAILABLE_MODELS, LLM_CONFIG, EMBEDDING_CONFIG
        )
        
        print(f"✅ Modelos disponibles: {AVAILABLE_MODELS}")
        print(f"✅ LLM Provider: {LLM_CONFIG['provider']}")
        print(f"✅ LLM Model: {LLM_CONFIG['model']}")
        print(f"✅ Embedding Provider: {EMBEDDING_CONFIG['provider']}")
        print(f"✅ Embedding Model: {EMBEDDING_CONFIG['model']}")
        
        # Verificar que Nova sea el modelo configurado
        if AVAILABLE_MODELS.get("bedrock", False):
            assert "nova-micro" in LLM_CONFIG['model'], f"Expected nova-micro, got {LLM_CONFIG['model']}"
            print("✅ Amazon Nova Micro configurado correctamente")
        else:
            print("⚠️  Bedrock no disponible")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        return False


async def test_nova_bedrock_agent():
    """Prueba el agente Bedrock con Nova"""
    print("\n🤖 Probando agente Bedrock con Nova...")
    
    try:
        from agent.bedrock_rag_agent import BedrockRAGAgent
        
        # Crear agente
        agent = BedrockRAGAgent()
        print("✅ Agente Bedrock con Nova creado")
        print(f"✅ Modelo LLM: {agent.llm_model}")
        print(f"✅ Modelo Embedding: {agent.embedding_model}")
        
        # Probar generación de respuesta
        print("💬 Probando generación de respuesta...")
        response = await agent.generate_response(
            "¿Qué es Amazon Nova? Responde brevemente.",
            "Eres un asistente útil que responde en español."
        )
        print(f"✅ Respuesta: {response}")
        
        # Probar embedding
        print("🔤 Probando embeddings...")
        embedding = await agent.generate_embedding("Texto de prueba para Nova")
        print(f"✅ Embedding generado: {len(embedding)} dimensiones")
        
        return agent
        
    except Exception as e:
        print(f"❌ Error en agente Nova: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_nova_rag_pipeline(agent):
    """Prueba el pipeline RAG completo con Nova"""
    print("\n🔄 Probando pipeline RAG con Nova...")
    
    try:
        # Crear documento de prueba sobre Nova
        docs_dir = Path("docs")
        if not docs_dir.exists():
            docs_dir.mkdir(exist_ok=True)
        
        nova_doc = docs_dir / "amazon_nova.md"
        nova_doc.write_text("""
# Amazon Nova

## ¿Qué es Amazon Nova?
Amazon Nova es la nueva familia de modelos de IA generativa de Amazon, diseñada para ser rápida, económica y de alta calidad.

## Modelos Nova Disponibles
- **Nova Micro**: El modelo más rápido y económico, ideal para tareas simples
- **Nova Lite**: Equilibrio entre velocidad y capacidad
- **Nova Pro**: Modelo avanzado para tareas complejas

## Características de Nova Micro
- Latencia ultra-baja
- Costo muy reducido
- Optimizado para respuestas rápidas
- Ideal para chatbots y asistentes

## Casos de Uso
- Chatbots de atención al cliente
- Respuestas automáticas
- Procesamiento de consultas simples
- Aplicaciones en tiempo real
""")
        
        # Cargar documentos
        success = await agent.load_documents()
        print(f"✅ Documentos cargados: {success}")
        
        # Probar consultas específicas sobre Nova
        test_queries = [
            "¿Qué es Amazon Nova?",
            "¿Cuáles son las características de Nova Micro?",
            "¿Para qué casos de uso es ideal Nova Micro?",
            "¿Qué modelos Nova están disponibles?"
        ]
        
        for query in test_queries:
            print(f"\n📝 Consulta: {query}")
            response = await agent.query(query, mode="hybrid")
            print(f"✅ Respuesta: {response[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en pipeline RAG: {e}")
        return False


async def test_nova_adk_integration():
    """Prueba la integración ADK con Nova"""
    print("\n🔗 Probando integración ADK con Nova...")
    
    try:
        from agent.bedrock_adk_integration import BedrockADKAgent
        
        # Crear agente ADK
        adk_agent = BedrockADKAgent()
        print("✅ Agente ADK con Nova creado")
        
        # Probar diferentes mensajes
        test_messages = [
            "probar modelos",
            "¿Qué ventajas tiene Amazon Nova Micro?",
            "estadísticas del sistema",
            "¿Por qué Nova es más rápido y barato?"
        ]
        
        for message in test_messages:
            print(f"\n📝 Mensaje: {message}")
            result = await adk_agent.process_message(message)
            
            if result['success']:
                print(f"✅ Respuesta exitosa")
                if 'response' in result:
                    print(f"   Contenido: {result['response'][:80]}...")
                elif 'results' in result:
                    print(f"   Resultados: {result['results']}")
            else:
                print(f"❌ Error: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en integración ADK: {e}")
        return False


async def test_nova_performance():
    """Prueba el rendimiento de Nova Micro"""
    print("\n⚡ Probando rendimiento de Nova Micro...")
    
    try:
        import time
        from agent.bedrock_rag_agent import BedrockRAGAgent
        
        agent = BedrockRAGAgent()
        
        # Probar velocidad de respuesta
        queries = [
            "¿Qué es IA?",
            "Explica machine learning",
            "¿Cómo funciona un chatbot?",
            "Define inteligencia artificial"
        ]
        
        total_time = 0
        successful_queries = 0
        
        for query in queries:
            start_time = time.time()
            
            try:
                response = await agent.generate_response(query)
                end_time = time.time()
                
                query_time = end_time - start_time
                total_time += query_time
                successful_queries += 1
                
                print(f"✅ Query: '{query[:30]}...' - Tiempo: {query_time:.2f}s")
                
            except Exception as e:
                print(f"❌ Error en query: {e}")
        
        if successful_queries > 0:
            avg_time = total_time / successful_queries
            print(f"\n📊 Rendimiento promedio: {avg_time:.2f} segundos por consulta")
            print(f"📊 Consultas exitosas: {successful_queries}/{len(queries)}")
            
            # Nova Micro debería ser muy rápido
            if avg_time < 3.0:
                print("🚀 ¡Excelente rendimiento! Nova Micro es muy rápido")
            elif avg_time < 5.0:
                print("✅ Buen rendimiento para Nova Micro")
            else:
                print("⚠️  Rendimiento más lento de lo esperado")
        
        return successful_queries > 0
        
    except Exception as e:
        print(f"❌ Error en prueba de rendimiento: {e}")
        return False


async def main():
    """Función principal"""
    print("🚀 Test completo de Amazon Nova Micro\n")
    
    results = {}
    
    # 1. Prueba directa de Nova Micro
    results['nova_direct'] = await test_nova_micro_direct()
    
    if not results['nova_direct']:
        print("\n❌ Nova Micro no disponible, verificar configuración")
        return
    
    # 2. Configuración
    results['configuration'] = await test_nova_configuration()
    
    # 3. Agente Bedrock con Nova
    agent = await test_nova_bedrock_agent()
    results['bedrock_agent'] = agent is not None
    
    if agent:
        # 4. Pipeline RAG
        results['rag_pipeline'] = await test_nova_rag_pipeline(agent)
        
        # 5. Rendimiento
        results['performance'] = await test_nova_performance()
    else:
        results['rag_pipeline'] = False
        results['performance'] = False
    
    # 6. Integración ADK
    results['adk_integration'] = await test_nova_adk_integration()
    
    # Resumen
    print("\n📊 Resumen de pruebas:")
    for test, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Resultado: {successful_tests}/{total_tests} pruebas exitosas")
    
    if successful_tests >= 4:
        print("\n🎉 ¡Amazon Nova Micro funcionando perfectamente!")
        print("\n✅ Funcionalidades verificadas:")
        print("- Acceso directo a Nova Micro")
        print("- Configuración correcta del agente")
        print("- Generación de respuestas rápidas")
        print("- Pipeline RAG completo")
        print("- Integración con Google ADK")
        print("- Rendimiento optimizado")
        
        print("\n💡 Ventajas de Nova Micro:")
        print("- ⚡ Latencia ultra-baja")
        print("- 💰 Costo muy reducido")
        print("- 🚀 Optimizado para velocidad")
        print("- 🔧 Ideal para aplicaciones en tiempo real")
        
        print("\n🚀 Comando para usar:")
        print("python test_nova_micro.py")
        
    else:
        print("⚠️  Algunas funcionalidades necesitan ajustes")


if __name__ == "__main__":
    asyncio.run(main())
